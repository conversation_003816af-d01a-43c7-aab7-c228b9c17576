import { defineConfig } from "vite";
import react from "@vitejs/plugin-react";
import federation from "@originjs/vite-plugin-federation";

// Example host application Vite configuration
export default defineConfig({
  plugins: [
    react(),
    federation({
      name: "host_app",
      remotes: {
        // Development URL - adjust as needed
        bot_ui: {
          external: "http://localhost:5173/remoteEntry.js",
          externalType: "url",
          format: "esm",
        },

        // Production URL - uncomment and adjust when deploying to production
        // bot_ui: {
        //   external: 'https://your-production-url.com/remoteEntry.js',
        //   externalType: 'url',
        //   format: 'esm'
        // },
      },
      shared: {
        // Shared dependencies to avoid duplicates
        react: { singleton: true, eager: true },
        "react-dom": { singleton: true, eager: true },
        "react-router-dom": { singleton: true },
        "@reduxjs/toolkit": { singleton: true },
        "react-redux": { singleton: true },
      },
    }),
  ],
  build: {
    modulePreload: false,
    target: "esnext",
    minify: false,
    cssCodeSplit: false,
  },
});
