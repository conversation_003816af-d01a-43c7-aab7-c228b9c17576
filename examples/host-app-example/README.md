# Host Application Example

This is an example of how to integrate the Bot UI micro-frontend into a host application.

## Setup

1. Install dependencies:

```bash
npm install
```

2. Start the Bot UI micro-frontend:

```bash
# In the Bot UI project directory
npm run dev:federation
```

3. Start the host application:

```bash
npm run dev
```

## How It Works

This example demonstrates:

1. Loading components from the Bot UI micro-frontend
2. Using the routes defined in the Bot UI
3. Integrating with the Bot UI's Redux store
4. Sharing dependencies between the host and remote applications

## Important Files

- `vite.config.ts`: Configuration for Module Federation
- `src/App.tsx`: Main application that imports and uses the micro-frontend
- `src/main.tsx`: Entry point for the host application

## Production Deployment

For production deployment, update the remote URL in `vite.config.ts` to point to the production URL of the Bot UI micro-frontend.
