import React, { Suspense } from 'react';

// Import the SimpleTestComponent using dynamic import
const SimpleTestComponent = React.lazy(() => 
  import('bot_ui/components/SimpleTestComponent').then(module => ({ 
    default: module.default 
  }))
);

function SimpleTest() {
  return (
    <div style={{ padding: '20px' }}>
      <h1>Host Application</h1>
      <p>Testing micro-frontend integration with SimpleTestComponent:</p>
      
      <Suspense fallback={<div>Loading SimpleTestComponent...</div>}>
        <SimpleTestComponent />
      </Suspense>
    </div>
  );
}

export default SimpleTest;
