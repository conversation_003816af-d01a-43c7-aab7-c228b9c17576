import React, { Suspense, lazy } from 'react';
import { BrowserRouter, Routes, Route, Link } from 'react-router-dom';
import { Provider } from 'react-redux';
import { configureStore } from '@reduxjs/toolkit';

// Lazy load components from the micro-frontend
// Note: In a real application, you might want to handle loading errors
const BotHome = lazy(() => import('bot_ui/routes/Home'));
const BotAgents = lazy(() => import('bot_ui/routes/Agents'));
const BotNewAgent = lazy(() => import('bot_ui/routes/NewAgent'));
const RouterExample = lazy(() => import('bot_ui/components/RouterExample'));

// Import routes from the micro-frontend
// Note: This is a dynamic import to ensure it's loaded after the remotes are initialized
const routes = await import('bot_ui/routes').then(module => module.default);

// Import and integrate the Redux store
// In a real application, you would combine this with your host app's reducers
const { store } = await import('bot_ui/store');

function App() {
  return (
    <Provider store={store}>
      <BrowserRouter>
        <div className="app">
          <header className="app-header">
            <h1>Host Application</h1>
            <nav>
              <ul>
                <li><Link to="/">Host Home</Link></li>
                <li><Link to="/bot">Bot UI Home</Link></li>
                <li><Link to="/bot/agents">Bot UI Agents</Link></li>
                <li><Link to="/bot/agents/new">Bot UI New Agent</Link></li>
                <li><Link to="/router-example">Router Example</Link></li>
              </ul>
            </nav>
          </header>

          <main>
            <Suspense fallback={<div>Loading...</div>}>
              <Routes>
                <Route path="/" element={<HostHome />} />
                <Route path="/bot" element={<BotHome />} />
                <Route path="/bot/agents" element={<BotAgents />} />
                <Route path="/bot/agents/new" element={<BotNewAgent />} />
                <Route path="/router-example" element={<RouterExample />} />
              </Routes>
            </Suspense>
          </main>
        </div>
      </BrowserRouter>
    </Provider>
  );
}

// A simple component for the host application's home page
function HostHome() {
  return (
    <div>
      <h2>Host Application Home</h2>
      <p>This is the host application that integrates the Bot UI micro-frontend.</p>
      <p>
        The Bot UI routes are available at:
      </p>
      <ul>
        <li>Home: {routes.HOME}</li>
        <li>Agents: {routes.AGENTS}</li>
        <li>New Agent: {routes.NEW_AGENT}</li>
      </ul>
    </div>
  );
}

export default App;
