# Bot UI Micro-Frontend

This project has been configured as a micro-frontend using Vite's Module Federation plugin. This allows you to integrate this UI into your host application while sharing dependencies.

## Building the Micro-Frontend

To build the micro-frontend for production:

```bash
npm run build:federation
```

This will generate the necessary federation manifest files in the `dist/federation` directory.

> **Note:** There are two separate build processes in this project:
>
> 1. `npm run build` - Uses React Router's build process for the standalone application
> 2. `npm run build:federation` - Uses Vite's build process for the micro-frontend with Module Federation

## Running in Development Mode

To run the micro-frontend in development mode:

```bash
npm run dev:federation
```

This will start the development server at `http://localhost:5173`.

## Running in Preview Mode

To preview the built micro-frontend:

```bash
npm run start:federation
```

This will serve the built files at `http://localhost:5173`.

## Integration with Host Application

### 1. Configure Your Host Application

In your host application's Vite configuration, add the Module Federation plugin:

```typescript
// vite.config.ts in your host application
import { defineConfig } from "vite";
import federation from "@originjs/vite-plugin-federation";

export default defineConfig({
  plugins: [
    // ... other plugins
    federation({
      name: "host_app",
      remotes: {
        bot_ui: {
          external: "http://localhost:5173/remoteEntry.js", // Development
          externalType: "url",
          format: "esm",
        },
        // Production URL - uncomment and adjust when deploying to production
        // bot_ui: {
        //   external: 'https://your-production-url.com/remoteEntry.js',
        //   externalType: 'url',
        //   format: 'esm'
        // },
      },
      shared: {
        react: { singleton: true },
        "react-dom": { singleton: true },
        "react-router": { singleton: true },
        "@reduxjs/toolkit": { singleton: true },
        "react-redux": { singleton: true },
      },
    }),
  ],
  build: {
    modulePreload: false,
    target: "esnext",
    minify: false,
    cssCodeSplit: false,
  },
});
```

### 2. Import and Use the Micro-Frontend

You can import components and utilities from the micro-frontend in your host application:

```typescript
// Import everything from the micro-frontend
import {
  App,
  routes,
  store,
  RouterExample,
  Home,
  Agents,
  NewAgents,
  useAppDispatch,
  useAppSelector,
  apiSlice,
  authReducer,
} from "bot_ui/";

// Or import specific components
import { RouterExample } from "bot_ui/components/RouterExample";
```

### 3. Integrating with Your Redux Store

If you want to integrate the micro-frontend's Redux store with your host application:

```typescript
import { combineReducers, configureStore } from "@reduxjs/toolkit";
import { apiSlice, authReducer } from "bot_ui/";

// Combine with your host application reducers
const rootReducer = combineReducers({
  // Your host application reducers
  hostFeature: hostFeatureReducer,

  // Micro-frontend reducers
  auth: authReducer,
  [apiSlice.reducerPath]: apiSlice.reducer,
});

export const store = configureStore({
  reducer: rootReducer,
  middleware: (getDefaultMiddleware) =>
    getDefaultMiddleware().concat(apiSlice.middleware),
});
```

### 4. Routing Integration

You can integrate the micro-frontend's routes with your host application's router:

```typescript
import { createBrowserRouter } from "react-router-dom";
import { routes, Home, Agents, NewAgents } from "bot_ui/";

const router = createBrowserRouter([
  // Your host application routes
  {
    path: "/",
    element: <YourHostLayout />,
    children: [
      // Micro-frontend routes
      {
        path: routes.HOME,
        element: <Home />,
      },
      {
        path: routes.AGENTS,
        element: <Agents />,
      },
      {
        path: routes.NEW_AGENT,
        element: <NewAgents />,
      },
    ],
  },
]);
```

## Available Exports

The micro-frontend exposes the following:

### Components

- `App`: The main App component
- `RouterExample`: Example component demonstrating router usage
- `Home`: Home page component
- `Agents`: Agents page component
- `NewAgents`: New Agent page component

### Utilities

- `routes`: Router configuration with static and dynamic routes
- `store`: Redux store
- `useAppDispatch`, `useAppSelector`: Typed Redux hooks
- `apiSlice`: API slice for data fetching
- `authReducer`, `setCredentials`, `unsetCredentials`: Authentication utilities

## Troubleshooting

### Empty remoteEntry.js File

If you see an empty white screen when accessing the remoteEntry.js file:

1. Make sure you're running the correct development server with `npm run dev:federation`
2. Check that the remoteEntry.js file is being properly served by visiting http://localhost:5173/remoteEntry.js
3. If the issue persists, try building the micro-frontend with `npm run build:federation` and then serving it with `npm run start:federation`
4. Ensure your host application is configured to use the correct URL format as shown in the examples

### CORS Issues

If you encounter CORS issues during development:

1. Make sure your development server has CORS enabled (the micro-frontend server has CORS enabled by default)
2. Check the browser console for specific CORS error messages
3. Try adding the following headers to your host application's server:
   ```
   "Access-Control-Allow-Origin": "*",
   "Access-Control-Allow-Methods": "GET, POST, PUT, DELETE, PATCH, OPTIONS",
   "Access-Control-Allow-Headers": "X-Requested-With, content-type, Authorization"
   ```

### Dependency Versioning

Ensure that the shared dependencies in both the host and remote applications have compatible versions:

1. It's recommended to use the same versions for critical dependencies like React, React DOM, and React Router
2. Check the `shared` configuration in both the host and remote applications to ensure they match
3. If you encounter version conflicts, try setting `singleton: true` for the affected dependencies

### Module Federation Connection Issues

If the host application cannot connect to the micro-frontend:

1. Ensure the micro-frontend is running and accessible
2. Check that the remoteEntry.js file is being properly served
3. Verify that the host application is configured to use the correct URL format
4. Try using the explicit configuration with `externalType` and `format` as shown in the examples

### Production Deployment

When deploying to production:

1. Build the micro-frontend with `npm run build:federation`
2. Deploy the contents of the `dist/federation` directory to your static hosting service
3. Update the remote URL in your host application's configuration to point to the production URL of the micro-frontend
4. Ensure the remoteEntry.js file is accessible at the specified URL
