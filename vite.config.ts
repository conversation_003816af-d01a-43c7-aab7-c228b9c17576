import { reactRouter } from "@react-router/dev/vite";
import tailwindcss from "@tailwindcss/vite";
import { defineConfig } from "vite";
import tsconfigPaths from "vite-tsconfig-paths";
import federation from "@originjs/vite-plugin-federation";
import { dependencies } from "./package.json";

// List of shared dependencies with the host application
const sharedDeps = {
  react: {
    requiredVersion: dependencies.react,
    singleton: true,
    eager: true,
  },
  "react-dom": {
    requiredVersion: dependencies["react-dom"],
    singleton: true,
    eager: true,
  },
  "react-router": {
    requiredVersion: dependencies["react-router"],
    singleton: true,
    eager: true,
  },
  "@reduxjs/toolkit": {
    requiredVersion: dependencies["@reduxjs/toolkit"],
    singleton: true,
  },
  "react-redux": {
    requiredVersion: dependencies["react-redux"],
    singleton: true,
  },
};

export default defineConfig({
  plugins: [
    tailwindcss(),
    reactRouter(),
    tsconfigPaths(),
    federation({
      name: "bot_ui",
      filename: "remoteEntry.js",
      exposes: {
        // Main federation entry point that exports everything
        "./": "./app/bootstrap.tsx",

        // Individual components and modules for direct access
        "components/MicroFrontendDemo":
          "./app/components/MicroFrontendDemo.tsx",
        "./App": "./app/root.tsx",
        "./routes": "./app/router/routes.ts",
        "./store": "./app/redux/store.ts",
        "./components/RouterExample": "./app/components/RouterExample.tsx",
        "./routes/Home": "./app/routes/Home/index.tsx",
        "./routes/Agents": "./app/routes/Agents/index.tsx",
        "./routes/NewAgent": "./app/routes/Agents/new.tsx",
      },
      shared: sharedDeps,
    }),
  ],
  build: {
    modulePreload: false,
    target: "esnext",
    minify: false,
    cssCodeSplit: false,
    rollupOptions: {
      output: {
        entryFileNames: "remoteEntry.js",
      },
    },
  },
  // Define the entry point for standalone mode
  server: {
    port: 5173,
    strictPort: true,
    cors: true,
  },
});
