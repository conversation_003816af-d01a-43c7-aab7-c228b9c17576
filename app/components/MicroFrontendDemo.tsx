import { useState } from "react";

/**
 * A simple demo component to verify that the micro-frontend integration is working correctly.
 * This component includes:
 * - State management with React hooks
 * - Styling with Tailwind CSS
 * - Interactive elements
 */
export default function MicroFrontendDemo() {
  const [count, setCount] = useState(0);
  const [message, setMessage] = useState("Hello from Bot UI Micro-Frontend!");

  const incrementCount = () => {
    setCount(count + 1);
  };

  const changeMessage = () => {
    const messages = [
      "Hello from Bot UI Micro-Frontend!",
      "Integration successful!",
      "Module Federation is working!",
      "You can now use all components from this micro-frontend!",
      "Congratulations on setting up your micro-frontend architecture!",
    ];

    const randomIndex = Math.floor(Math.random() * messages.length);
    setMessage(messages[randomIndex]);
  };

  return (
    <div className="p-6 max-w-md mx-auto bg-white rounded-xl shadow-md flex flex-col items-center space-y-4 mt-4">
      <div className="text-center">
        <h2 className="text-2xl font-bold text-blue-600">
          Micro-Frontend Demo
        </h2>
        <p className="text-gray-500 mt-2">
          This component is loaded from the Bot UI micro-frontend
        </p>
      </div>

      <div className="bg-blue-100 p-4 rounded-lg w-full text-center">
        <p className="text-lg font-semibold">{message}</p>
      </div>

      <div className="flex flex-col items-center space-y-2 w-full">
        <p className="text-gray-700">
          Counter: <span className="font-bold">{count}</span>
        </p>
        <div className="flex space-x-2">
          <button
            onClick={incrementCount}
            className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 transition-colors"
          >
            Increment
          </button>
          <button
            onClick={changeMessage}
            className="px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600 transition-colors"
          >
            Change Message
          </button>
        </div>
      </div>

      <div className="border-t border-gray-200 pt-4 w-full">
        <p className="text-sm text-gray-500 text-center">
          Current time: {new Date().toLocaleTimeString()}
        </p>
      </div>
    </div>
  );
}
