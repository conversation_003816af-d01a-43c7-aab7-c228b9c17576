import React from "react";
import { useAppDispatch, useAppSelector } from "../../hooks/redux-hooks";
import {
  setSearchQuery,
  setStatusFilter,
} from "../../redux/chatbots/chatbotsSlice";

const SearchAndFilters: React.FC = () => {
  const dispatch = useAppDispatch();
  const { searchQuery, statusFilter } = useAppSelector(
    (state) => state.chatbots
  );

  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    dispatch(setSearchQuery(e.target.value));
  };

  const handleStatusFilterChange = (status: "all" | "live" | "draft") => {
    dispatch(setStatusFilter(status));
  };

  return (
    <div className="flex items-center justify-between mb-6">
      {/* Search Bar */}
      <div className="flex-1 max-w-[320px]">
        <div className="relative">
          <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
            <svg
              className="h-4 w-4 text-[#94a3b8]"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
              />
            </svg>
          </div>
          <input
            type="text"
            placeholder="Search"
            value={searchQuery}
            onChange={handleSearchChange}
            className="block w-full pl-9 pr-4 py-2.5 border border-[#e2e8f0] rounded-[8px] leading-5 bg-white placeholder-[#94a3b8] focus:outline-none focus:placeholder-[#64748b] focus:ring-1 focus:ring-[#3b82f6] focus:border-[#3b82f6] text-sm"
          />
        </div>
      </div>

      {/* Filters and Actions */}
      <div className="flex items-center space-x-3">
        {/* Date Filter */}
        <div className="relative">
          <select className="appearance-none bg-white border border-[#e2e8f0] rounded-[8px] px-4 py-2.5 pr-10 text-sm font-medium focus:outline-none focus:ring-1 focus:ring-[#3b82f6] focus:border-[#3b82f6] shadow-sm">
            <option>Last 30 Days</option>
            <option>Last 7 Days</option>
            <option>Last 24 Hours</option>
            <option>All Time</option>
          </select>
          <div className="absolute inset-y-0 right-0 flex items-center px-3 pointer-events-none">
            <svg
              className="w-4 h-4 text-[#94a3b8]"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M19 9l-7 7-7-7"
              />
            </svg>
          </div>
        </div>

        {/* Status Filter Pills */}
        <div className="flex items-center space-x-2 bg-gray-50 rounded-xl p-1">
          <button
            onClick={() => handleStatusFilterChange("all")}
            className={`px-4 py-2 text-sm font-medium rounded-lg transition-all duration-200 ${
              statusFilter === "all"
                ? "bg-white text-gray-900 shadow-sm"
                : "text-gray-600 hover:text-gray-900 hover:bg-white/50"
            }`}
          >
            All
          </button>
          <button
            onClick={() => handleStatusFilterChange("live")}
            className={`px-4 py-2 text-sm font-medium rounded-lg transition-all duration-200 ${
              statusFilter === "live"
                ? "bg-white text-gray-900 shadow-sm"
                : "text-gray-600 hover:text-gray-900 hover:bg-white/50"
            }`}
          >
            Live
          </button>
          <button
            onClick={() => handleStatusFilterChange("draft")}
            className={`px-4 py-2 text-sm font-medium rounded-lg transition-all duration-200 ${
              statusFilter === "draft"
                ? "bg-white text-gray-900 shadow-sm"
                : "text-gray-600 hover:text-gray-900 hover:bg-white/50"
            }`}
          >
            Draft
          </button>
        </div>

        {/* Filter Button */}
        <button className="flex items-center space-x-2 px-4 py-2.5 border border-[#e2e8f0] rounded-[8px] text-sm font-medium text-[#64748b] hover:bg-[#f8fafc] transition-all duration-200 shadow-sm">
          <svg
            className="w-4 h-4"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.207A1 1 0 013 6.5V4z"
            />
          </svg>
          <span>Filter</span>
        </button>

        {/* View Toggle */}
        <div className="flex items-center border border-[#e2e8f0] rounded-[8px] overflow-hidden shadow-sm">
          <button className="p-2.5 text-[#64748b] hover:text-[#1e293b] bg-white hover:bg-[#f8fafc] transition-all duration-200">
            <svg
              className="w-4 h-4"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M4 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2V6zM14 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2V6zM4 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2v-2zM14 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2v-2z"
              />
            </svg>
          </button>
          <button className="p-2.5 text-[#94a3b8] hover:text-[#64748b] bg-[#f8fafc] hover:bg-[#f1f5f9] transition-all duration-200">
            <svg
              className="w-4 h-4"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M4 6h16M4 10h16M4 14h16M4 18h16"
              />
            </svg>
          </button>
        </div>

        {/* Create Button */}
        <button className="bg-[#EF4444] hover:bg-[#DC2626] text-white px-6 py-2.5 rounded-[8px] text-sm font-bold transition-all duration-200 shadow-md hover:shadow-lg transform hover:scale-105">
          CREATE
        </button>
      </div>
    </div>
  );
};

export default SearchAndFilters;
