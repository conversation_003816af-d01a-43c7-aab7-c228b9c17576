import React from "react";
import { useAppSelector, useAppDispatch } from "../../hooks/redux-hooks";
import { deleteChatbot } from "../../redux/chatbots/chatbotsSlice";
import ChatbotCard from "./ChatbotCard";
import { Chatbot } from "../../types";

const ChatbotGrid: React.FC = () => {
  const dispatch = useAppDispatch();
  const { chatbots, searchQuery, statusFilter, loading } = useAppSelector(
    (state) => state.chatbots
  );

  // Filter chatbots based on search query and status filter
  const filteredChatbots = chatbots.filter((chatbot: Chatbot) => {
    const matchesSearch =
      chatbot.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      chatbot.description.toLowerCase().includes(searchQuery.toLowerCase());
    const matchesStatus =
      statusFilter === "all" || chatbot.status === statusFilter;
    return matchesSearch && matchesStatus;
  });

  const handleDelete = (id: string) => {
    if (window.confirm("Are you sure you want to delete this chatbot?")) {
      dispatch(deleteChatbot(id));
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-red-600"></div>
      </div>
    );
  }

  if (filteredChatbots.length === 0) {
    return (
      <div className="text-center py-12">
        <div className="w-16 h-16 mx-auto mb-4 bg-gray-100 rounded-full flex items-center justify-center">
          <svg
            className="w-8 h-8 text-gray-400"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M9.172 16.172a4 4 0 015.656 0M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
            />
          </svg>
        </div>
        <h3 className="text-lg font-medium text-gray-900 mb-2">
          No chatbots found
        </h3>
        <p className="text-gray-500 mb-4">
          {searchQuery || statusFilter !== "all"
            ? "Try adjusting your search or filters to find what you're looking for."
            : "Get started by creating your first chatbot."}
        </p>
        <button className="bg-red-600 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-red-700 transition-colors">
          Create Chatbot
        </button>
      </div>
    );
  }

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-5">
      {filteredChatbots.map((chatbot) => (
        <ChatbotCard
          key={chatbot.id}
          chatbot={chatbot}
          onDelete={handleDelete}
        />
      ))}
    </div>
  );
};

export default ChatbotGrid;
