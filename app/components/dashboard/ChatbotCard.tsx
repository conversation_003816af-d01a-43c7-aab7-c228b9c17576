import React from "react";
import { <PERSON> } from "react-router";
import { Chatbot } from "../../types";

interface ChatbotCardProps {
  chatbot: Chatbot;
  onEdit?: (id: string) => void;
  onDelete?: (id: string) => void;
}

const ChatbotCard: React.FC<ChatbotCardProps> = ({
  chatbot,
  onEdit,
  onDelete,
}) => {
  const statusConfig =
    chatbot.status === "live"
      ? {
          bgColor: "bg-emerald-500",
          textColor: "text-white",
          dotColor: "bg-white",
          text: "Live",
        }
      : {
          bgColor: "bg-gray-400",
          textColor: "text-white",
          dotColor: "bg-white",
          text: "Draft",
        };

  return (
    <div className="bg-white rounded-[12px] border border-[#e2e8f0] p-4 hover:shadow-[0_8px_25px_rgba(0,0,0,0.1)] hover:border-[#cbd5e1] transition-all duration-200 group">
      {/* Header with Robot Icon and Status */}
      <div className="flex items-start justify-between mb-3">
        {/* Robot Icon - Simplified design matching the image */}
        <div className="w-10 h-10 bg-[#f1f5f9] rounded-[8px] flex items-center justify-center">
          <div className="w-7 h-7 bg-[#64748b] rounded-[5px] flex items-center justify-center">
            <div className="w-4 h-4 bg-white rounded-[2px] flex items-center justify-center">
              <div className="w-1.5 h-1.5 bg-[#64748b] rounded-full"></div>
            </div>
          </div>
        </div>

        {/* Status Badge */}
        <div
          className={`px-3 py-1 rounded-full text-xs font-medium ${statusConfig.bgColor} ${statusConfig.textColor}`}
        >
          <div className="flex items-center space-x-1">
            <div
              className={`w-1.5 h-1.5 ${statusConfig.dotColor} rounded-full`}
            ></div>
            <span>{statusConfig.text}</span>
          </div>
        </div>
      </div>

      {/* Content */}
      <div className="mb-4">
        <h3 className="text-base font-semibold text-[#1e293b] mb-1.5 group-hover:text-[#0f172a] transition-colors">
          {chatbot.name}
        </h3>
        <p className="text-sm text-[#64748b] leading-relaxed line-clamp-2">
          {chatbot.description}
        </p>
      </div>

      {/* Footer */}
      <div className="flex items-center justify-between pt-3 border-t border-[#f1f5f9]">
        <div className="text-xs text-[#94a3b8] font-medium">
          Last updated {chatbot.lastUpdated}
        </div>

        {/* Actions */}
        <div className="flex items-center space-x-1">
          <Link
            to={`/builder/${chatbot.id}`}
            className="p-1.5 text-[#94a3b8] hover:text-[#3b82f6] hover:bg-[#eff6ff] rounded-md transition-all duration-200"
            title="Edit"
          >
            <svg
              className="w-4 h-4"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"
              />
            </svg>
          </Link>

          <button
            onClick={() => onDelete?.(chatbot.id)}
            className="p-1.5 text-[#94a3b8] hover:text-[#ef4444] hover:bg-[#fef2f2] rounded-md transition-all duration-200"
            title="Delete"
          >
            <svg
              className="w-4 h-4"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"
              />
            </svg>
          </button>

          <button className="p-1.5 text-[#94a3b8] hover:text-[#64748b] hover:bg-[#f8fafc] rounded-md transition-all duration-200">
            <svg
              className="w-4 h-4"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M12 5v.01M12 12v.01M12 19v.01M12 6a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2z"
              />
            </svg>
          </button>
        </div>
      </div>
    </div>
  );
};

export default ChatbotCard;
