import React from "react";
import { FlowNode } from "../../types";

interface NodeComponentProps {
  node: FlowNode;
  isSelected: boolean;
  onClick: () => void;
  onDragStart: (e: React.MouseEvent) => void;
}

const NodeComponent: React.FC<NodeComponentProps> = ({
  node,
  isSelected,
  onClick,
  onDragStart,
}) => {
  const getNodeIcon = (type: FlowNode["type"]) => {
    switch (type) {
      case "start":
        return (
          <svg
            className="w-3.5 h-3.5 text-white"
            fill="currentColor"
            viewBox="0 0 24 24"
          >
            <path d="M8 5v14l11-7z" />
          </svg>
        );
      case "message":
        return (
          <svg
            className="w-3.5 h-3.5 text-white"
            fill="currentColor"
            viewBox="0 0 24 24"
          >
            <path d="M20 2H4c-1.1 0-2 .9-2 2v12c0 1.1.9 2 2 2h14l4 4V4c0-1.1-.9-2-2-2zm-2 12H6v-2h12v2zm0-3H6V9h12v2zm0-3H6V6h12v2z" />
          </svg>
        );
      case "interactive":
        return (
          <svg
            className="w-3.5 h-3.5 text-white"
            fill="currentColor"
            viewBox="0 0 24 24"
          >
            <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z" />
          </svg>
        );
      case "feedback":
        return (
          <svg
            className="w-3.5 h-3.5 text-white"
            fill="currentColor"
            viewBox="0 0 24 24"
          >
            <path d="M12 17.27L18.18 21l-1.64-7.03L22 9.24l-7.19-.61L12 2 9.19 8.63 2 9.24l5.46 4.73L5.82 21z" />
          </svg>
        );
      case "notification":
        return (
          <svg
            className="w-3.5 h-3.5 text-white"
            fill="currentColor"
            viewBox="0 0 24 24"
          >
            <path d="M12 22c1.1 0 2-.9 2-2h-4c0 1.1.89 2 2 2zm6-6v-5c0-3.07-1.64-5.64-4.5-6.32V4c0-.83-.67-1.5-1.5-1.5s-1.5.67-1.5 1.5v.68C7.63 5.36 6 7.92 6 11v5l-2 2v1h16v-1l-2-2z" />
          </svg>
        );
      default:
        return (
          <svg
            className="w-3.5 h-3.5 text-white"
            fill="currentColor"
            viewBox="0 0 24 24"
          >
            <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2z" />
          </svg>
        );
    }
  };

  const getNodeColor = (type: FlowNode["type"]) => {
    switch (type) {
      case "start":
        return "bg-white border-[#10b981] shadow-sm";
      case "message":
        return "bg-white border-[#3b82f6] shadow-sm";
      case "interactive":
        return "bg-white border-[#10b981] shadow-sm";
      case "feedback":
        return "bg-white border-[#f59e0b] shadow-sm";
      case "notification":
        return "bg-white border-[#8b5cf6] shadow-sm";
      default:
        return "bg-white border-[#6b7280] shadow-sm";
    }
  };

  const getIconBgColor = (type: FlowNode["type"]) => {
    switch (type) {
      case "start":
        return "bg-[#10b981]";
      case "message":
        return "bg-[#3b82f6]";
      case "interactive":
        return "bg-[#10b981]";
      case "feedback":
        return "bg-[#f59e0b]";
      case "notification":
        return "bg-[#8b5cf6]";
      default:
        return "bg-[#6b7280]";
    }
  };

  const getNodeLabel = (node: FlowNode) => {
    return (
      node.data.label || node.type.charAt(0).toUpperCase() + node.type.slice(1)
    );
  };

  return (
    <div
      className={`absolute cursor-move select-none transition-all duration-200 ${
        isSelected ? "z-10" : "z-0"
      }`}
      style={{
        left: node.position.x,
        top: node.position.y,
        transform: isSelected ? "scale(1.05)" : "scale(1)",
      }}
      onClick={(e) => {
        e.stopPropagation();
        onClick();
      }}
      onMouseDown={onDragStart}
    >
      {/* Node Container */}
      <div
        className={`
          w-[160px] min-h-[50px] rounded-lg border p-3 shadow-sm hover:shadow-md transition-all duration-200
          ${getNodeColor(node.type)}
          ${isSelected ? "ring-2 ring-[#3b82f6] ring-offset-2" : ""}
        `}
      >
        {/* Node Header */}
        <div className="flex items-center space-x-2 mb-1">
          <div
            className={`w-6 h-6 rounded flex items-center justify-center ${getIconBgColor(
              node.type
            )}`}
          >
            {getNodeIcon(node.type)}
          </div>
          <span className="font-medium text-sm text-[#1e293b]">
            {getNodeLabel(node)}
          </span>
        </div>

        {/* Node Content */}
        {node.data.message && (
          <div className="text-xs text-gray-600 mb-2 line-clamp-2">
            {node.data.message}
          </div>
        )}

        {/* Node Options */}
        {node.data.options && node.data.options.length > 0 && (
          <div className="space-y-1">
            {node.data.options.slice(0, 2).map((option, index) => (
              <div
                key={index}
                className="text-xs bg-white bg-opacity-50 rounded px-2 py-1"
              >
                {option}
              </div>
            ))}
            {node.data.options.length > 2 && (
              <div className="text-xs text-gray-500">
                +{node.data.options.length - 2} more
              </div>
            )}
          </div>
        )}

        {/* Connection Points */}
        <div className="absolute -right-1.5 top-1/2 transform -translate-y-1/2">
          <div className="w-3 h-3 bg-white border-2 border-[#94a3b8] rounded-full hover:border-[#3b82f6] transition-colors cursor-pointer"></div>
        </div>

        <div className="absolute -left-1.5 top-1/2 transform -translate-y-1/2">
          <div className="w-3 h-3 bg-white border-2 border-[#94a3b8] rounded-full hover:border-[#3b82f6] transition-colors cursor-pointer"></div>
        </div>
      </div>

      {/* Node Actions (visible when selected) */}
      {isSelected && (
        <div className="absolute -top-8 right-0 flex space-x-1">
          <button
            className="w-6 h-6 bg-white border border-gray-300 rounded text-xs hover:bg-gray-50 transition-colors"
            title="Edit"
          >
            ✏️
          </button>
          <button
            className="w-6 h-6 bg-white border border-gray-300 rounded text-xs hover:bg-red-50 hover:border-red-300 transition-colors"
            title="Delete"
          >
            🗑️
          </button>
        </div>
      )}
    </div>
  );
};

export default NodeComponent;
