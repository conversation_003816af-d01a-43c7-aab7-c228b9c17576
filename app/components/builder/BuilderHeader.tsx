import React from "react";

const BuilderHeader: React.FC = () => {
  return (
    <div className="bg-white border-b border-[#e2e8f0] px-6 py-4">
      <div className="flex items-center justify-between">
        {/* Left side - Breadcrumb and Bot Info */}
        <div className="flex items-center space-x-4">
          {/* Breadcrumb */}
          <div className="flex items-center space-x-2 text-sm">
            <span className="text-[#64748b]">NeuraTalk</span>
            <svg
              className="w-4 h-4 text-[#cbd5e1]"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M9 5l7 7-7 7"
              />
            </svg>
            <span className="text-[#3b82f6] font-medium">Create</span>
          </div>

          {/* Bot Info */}
          <div className="flex items-center space-x-3 ml-6">
            <div className="w-8 h-8 bg-[#f1f5f9] rounded-lg flex items-center justify-center">
              <svg
                className="w-5 h-5 text-[#64748b]"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"
                />
              </svg>
            </div>
            <div>
              <div className="flex items-center space-x-2">
                <h1 className="text-lg font-semibold text-[#1e293b]">My Chatbot</h1>
                <button className="p-1 text-[#94a3b8] hover:text-[#64748b] transition-colors">
                  <svg
                    className="w-4 h-4"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z"
                    />
                  </svg>
                </button>
              </div>
              <p className="text-sm text-[#64748b]">
                Help customers navigate the digital purchasing process.
              </p>
            </div>
          </div>
        </div>

        {/* Right side - Action buttons */}
        <div className="flex items-center space-x-3">
          <button className="px-4 py-2 text-sm font-medium text-[#64748b] bg-white border border-[#d1d5db] rounded-lg hover:bg-[#f9fafb] transition-colors">
            PREVIEW
          </button>
          <button className="px-4 py-2 text-sm font-medium text-white bg-[#3b82f6] rounded-lg hover:bg-[#2563eb] transition-colors">
            PUBLISH
          </button>
        </div>
      </div>
    </div>
  );
};

export default BuilderHeader;
