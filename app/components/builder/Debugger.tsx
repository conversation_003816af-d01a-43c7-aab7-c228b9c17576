import React from 'react';
import { useAppSelector, useAppDispatch } from '../../hooks/redux-hooks';
import { toggleDebugger, clearDebugLogs } from '../../redux/builder/builderSlice';

const Debugger: React.FC = () => {
  const dispatch = useAppDispatch();
  const { debuggerOpen, debugLogs } = useAppSelector(state => state.builder);

  const getLogIcon = (type: 'error' | 'warning' | 'info') => {
    switch (type) {
      case 'error':
        return '🔴';
      case 'warning':
        return '🟡';
      case 'info':
        return '🔵';
      default:
        return '⚪';
    }
  };

  const getLogColor = (type: 'error' | 'warning' | 'info') => {
    switch (type) {
      case 'error':
        return 'text-red-600 bg-red-50 border-red-200';
      case 'warning':
        return 'text-yellow-600 bg-yellow-50 border-yellow-200';
      case 'info':
        return 'text-blue-600 bg-blue-50 border-blue-200';
      default:
        return 'text-gray-600 bg-gray-50 border-gray-200';
    }
  };

  if (!debuggerOpen) {
    return (
      <button
        onClick={() => dispatch(toggleDebugger())}
        className="fixed bottom-4 left-4 bg-gray-800 text-white px-4 py-2 rounded-lg shadow-lg hover:bg-gray-700 transition-colors flex items-center space-x-2"
      >
        <span>🐛</span>
        <span className="text-sm">Debugger</span>
        {debugLogs.length > 0 && (
          <span className="bg-red-500 text-white text-xs px-2 py-1 rounded-full">
            {debugLogs.length}
          </span>
        )}
      </button>
    );
  }

  return (
    <div className="fixed bottom-0 left-0 right-0 bg-white border-t border-gray-200 shadow-lg" style={{ height: '300px' }}>
      {/* Header */}
      <div className="flex items-center justify-between p-3 border-b border-gray-200 bg-gray-50">
        <div className="flex items-center space-x-3">
          <span className="text-lg">🐛</span>
          <h3 className="font-semibold text-gray-900">Debugger</h3>
          {debugLogs.length > 0 && (
            <span className="bg-gray-200 text-gray-700 text-xs px-2 py-1 rounded-full">
              {debugLogs.length} log{debugLogs.length !== 1 ? 's' : ''}
            </span>
          )}
        </div>
        
        <div className="flex items-center space-x-2">
          <button
            onClick={() => dispatch(clearDebugLogs())}
            className="text-sm text-gray-600 hover:text-gray-900 transition-colors"
            title="Clear logs"
          >
            Clear
          </button>
          <button
            onClick={() => dispatch(toggleDebugger())}
            className="text-gray-400 hover:text-gray-600 transition-colors"
            title="Close debugger"
          >
            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>
      </div>

      {/* Tabs */}
      <div className="flex border-b border-gray-200 bg-gray-50">
        <button className="px-4 py-2 text-sm font-medium text-gray-900 border-b-2 border-blue-500 bg-white">
          Console
        </button>
        <button className="px-4 py-2 text-sm font-medium text-gray-600 hover:text-gray-900 transition-colors">
          Debug
        </button>
      </div>

      {/* Logs */}
      <div className="flex-1 overflow-y-auto p-3 space-y-2" style={{ height: 'calc(100% - 100px)' }}>
        {debugLogs.length === 0 ? (
          <div className="text-center text-gray-500 mt-8">
            <div className="w-12 h-12 mx-auto mb-3 bg-gray-100 rounded-full flex items-center justify-center">
              <svg className="w-6 h-6 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
              </svg>
            </div>
            <p className="text-sm">No debug logs yet</p>
          </div>
        ) : (
          debugLogs.map((log) => (
            <div
              key={log.id}
              className={`border rounded-lg p-3 ${getLogColor(log.type)}`}
            >
              <div className="flex items-start space-x-2">
                <span className="text-sm">{getLogIcon(log.type)}</span>
                <div className="flex-1 min-w-0">
                  <div className="flex items-center justify-between">
                    <span className="text-xs font-medium uppercase tracking-wide">
                      {log.type}
                    </span>
                    <span className="text-xs text-gray-500">
                      {new Date(log.timestamp).toLocaleTimeString()}
                    </span>
                  </div>
                  <p className="text-sm mt-1 break-words">{log.message}</p>
                  {log.details && (
                    <details className="mt-2">
                      <summary className="text-xs cursor-pointer hover:underline">
                        Show details
                      </summary>
                      <pre className="text-xs mt-1 p-2 bg-white bg-opacity-50 rounded overflow-x-auto">
                        {log.details}
                      </pre>
                    </details>
                  )}
                </div>
              </div>
            </div>
          ))
        )}
      </div>
    </div>
  );
};

export default Debugger;
