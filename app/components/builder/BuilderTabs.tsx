import React from "react";
import { useAppSelector, useAppDispatch } from "../../hooks/redux-hooks";
import { setMode } from "../../redux/builder/builderSlice";

const BuilderTabs: React.FC = () => {
  const dispatch = useAppDispatch();
  const { mode } = useAppSelector((state) => state.builder);

  const tabs = [
    { id: "design", label: "Design" },
    { id: "train", label: "Train" },
    { id: "channels", label: "Channels" },
    { id: "agent-transfer", label: "Agent Transfer" },
    { id: "integrations", label: "Integrations" },
    { id: "settings", label: "Settings" },
  ] as const;

  const handleTabClick = (tabId: typeof mode) => {
    dispatch(setMode(tabId));
  };

  return (
    <div className="bg-[#f8fafc] border-b border-[#e2e8f0]">
      {/* Tabs */}
      <div className="flex px-6">
        {tabs.map((tab) => (
          <button
            key={tab.id}
            onClick={() => handleTabClick(tab.id)}
            className={`
              px-4 py-3 text-sm font-medium border-b-2 transition-all duration-200 relative
              ${
                mode === tab.id
                  ? "border-[#3b82f6] text-[#3b82f6] font-semibold bg-white"
                  : "border-transparent text-[#64748b] hover:text-[#1e293b] hover:bg-white/50"
              }
            `}
            style={
              mode === tab.id
                ? {
                    borderRadius: "6px 6px 0 0",
                    marginBottom: "-1px",
                  }
                : {}
            }
          >
            {tab.label}
          </button>
        ))}
      </div>
    </div>
  );
};

export default BuilderTabs;
