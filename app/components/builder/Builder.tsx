import React from "react";
import { useAppSelector } from "../../hooks/redux-hooks";
import BuilderTabs from "./BuilderTabs";
import BuilderHeader from "./BuilderHeader";
import LeftNavigation from "./LeftNavigation";
import FlowSidebar from "./FlowSidebar";
import FlowCanvas from "./FlowCanvas";
import NodesSidebar from "./NodesSidebar";
import ChatPreview from "./ChatPreview";
import Debugger from "./Debugger";

const Builder: React.FC = () => {
  const { mode } = useAppSelector((state) => state.builder);

  const renderContent = () => {
    switch (mode) {
      case "design":
        return (
          <div className="flex flex-1 overflow-hidden">
            <FlowSidebar />
            <FlowCanvas />
            <NodesSidebar />
          </div>
        );
      case "train":
        return (
          <div className="flex-1 p-6">
            <div className="text-center py-12">
              <div className="w-16 h-16 mx-auto mb-4 bg-gray-100 rounded-full flex items-center justify-center">
                <span className="text-2xl">🧠</span>
              </div>
              <h3 className="text-lg font-medium text-gray-900 mb-2">
                Training Mode
              </h3>
              <p className="text-gray-500">
                Train your chatbot with conversation data and improve its
                responses.
              </p>
            </div>
          </div>
        );
      case "channels":
        return (
          <div className="flex-1 p-6">
            <div className="text-center py-12">
              <div className="w-16 h-16 mx-auto mb-4 bg-gray-100 rounded-full flex items-center justify-center">
                <span className="text-2xl">📱</span>
              </div>
              <h3 className="text-lg font-medium text-gray-900 mb-2">
                Channels
              </h3>
              <p className="text-gray-500">
                Configure deployment channels for your chatbot.
              </p>
            </div>
          </div>
        );
      case "agent-transfer":
        return (
          <div className="flex-1 p-6">
            <div className="text-center py-12">
              <div className="w-16 h-16 mx-auto mb-4 bg-gray-100 rounded-full flex items-center justify-center">
                <span className="text-2xl">👥</span>
              </div>
              <h3 className="text-lg font-medium text-gray-900 mb-2">
                Agent Transfer
              </h3>
              <p className="text-gray-500">
                Set up seamless handoff to human agents when needed.
              </p>
            </div>
          </div>
        );
      case "integrations":
        return (
          <div className="flex-1 p-6">
            <div className="text-center py-12">
              <div className="w-16 h-16 mx-auto mb-4 bg-gray-100 rounded-full flex items-center justify-center">
                <span className="text-2xl">🔗</span>
              </div>
              <h3 className="text-lg font-medium text-gray-900 mb-2">
                Integrations
              </h3>
              <p className="text-gray-500">
                Connect your chatbot with external services and APIs.
              </p>
            </div>
          </div>
        );
      case "settings":
        return (
          <div className="flex-1 p-6">
            <div className="text-center py-12">
              <div className="w-16 h-16 mx-auto mb-4 bg-gray-100 rounded-full flex items-center justify-center">
                <span className="text-2xl">⚙️</span>
              </div>
              <h3 className="text-lg font-medium text-gray-900 mb-2">
                Settings
              </h3>
              <p className="text-gray-500">
                Configure your chatbot settings and preferences.
              </p>
            </div>
          </div>
        );
      default:
        return (
          <div className="flex flex-1 overflow-hidden">
            <FlowCanvas />
            <FlowSidebar />
          </div>
        );
    }
  };

  return (
    <div className="h-screen flex bg-[#fafbfc]">
      <LeftNavigation />
      <div className="flex-1 flex flex-col">
        <BuilderHeader />
        <BuilderTabs />
        {renderContent()}
      </div>
    </div>
  );
};

export default Builder;
