import React from "react";

const NodesSidebar: React.FC = () => {
  const nodeCategories = [
    {
      title: "Usage",
      nodes: [
        {
          id: "message",
          label: "Message",
          icon: (
            <svg
              className="w-3.5 h-3.5 text-white"
              fill="currentColor"
              viewBox="0 0 24 24"
            >
              <path d="M20 2H4c-1.1 0-2 .9-2 2v12c0 1.1.9 2 2 2h14l4 4V4c0-1.1-.9-2-2-2zm-2 12H6v-2h12v2zm0-3H6V9h12v2zm0-3H6V6h12v2z" />
            </svg>
          ),
          bgColor: "bg-[#3b82f6]",
          containerBg: "bg-[#eff6ff]",
          containerBorder: "border-[#dbeafe]",
          hoverBg: "hover:bg-[#dbeafe]",
        },
        {
          id: "interactive",
          label: "Interactive Message",
          icon: (
            <svg
              className="w-3.5 h-3.5 text-white"
              fill="currentColor"
              viewBox="0 0 24 24"
            >
              <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z" />
            </svg>
          ),
          bgColor: "bg-[#10b981]",
          containerBg: "bg-[#ecfdf5]",
          containerBorder: "border-[#d1fae5]",
          hoverBg: "hover:bg-[#d1fae5]",
        },
        {
          id: "feedback",
          label: "Feedback",
          icon: (
            <svg
              className="w-3.5 h-3.5 text-white"
              fill="currentColor"
              viewBox="0 0 24 24"
            >
              <path d="M12 17.27L18.18 21l-1.64-7.03L22 9.24l-7.19-.61L12 2 9.19 8.63 2 9.24l5.46 4.73L5.82 21z" />
            </svg>
          ),
          bgColor: "bg-[#f59e0b]",
          containerBg: "bg-[#fffbeb]",
          containerBorder: "border-[#fed7aa]",
          hoverBg: "hover:bg-[#fed7aa]",
        },
      ],
    },
    {
      title: "Utilities",
      nodes: [
        {
          id: "utilities",
          label: "Utilities",
          icon: (
            <svg
              className="w-3.5 h-3.5 text-white"
              fill="currentColor"
              viewBox="0 0 24 24"
            >
              <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z" />
            </svg>
          ),
          bgColor: "bg-[#6366f1]",
          containerBg: "bg-[#eef2ff]",
          containerBorder: "border-[#c7d2fe]",
          hoverBg: "hover:bg-[#c7d2fe]",
        },
      ],
    },
    {
      title: "Marketplace",
      nodes: [
        {
          id: "notification",
          label: "Notification",
          icon: (
            <svg
              className="w-3.5 h-3.5 text-white"
              fill="currentColor"
              viewBox="0 0 24 24"
            >
              <path d="M12 22c1.1 0 2-.9 2-2h-4c0 1.1.89 2 2 2zm6-6v-5c0-3.07-1.64-5.64-4.5-6.32V4c0-.83-.67-1.5-1.5-1.5s-1.5.67-1.5 1.5v.68C7.63 5.36 6 7.92 6 11v5l-2 2v1h16v-1l-2-2z" />
            </svg>
          ),
          bgColor: "bg-[#8b5cf6]",
          containerBg: "bg-[#f5f3ff]",
          containerBorder: "border-[#e9d5ff]",
          hoverBg: "hover:bg-[#e9d5ff]",
        },
      ],
    },
  ];

  return (
    <div className="w-72 bg-white border-l border-[#e2e8f0] flex flex-col shadow-sm">
      {/* Header */}
      <div className="p-4 border-b border-[#f1f5f9]">
        <div className="flex items-center justify-between">
          <h3 className="text-base font-semibold text-[#1e293b]">Nodes</h3>
          <button className="p-1 text-[#94a3b8] hover:text-[#64748b] transition-colors">
            <svg
              className="w-4 h-4"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
              />
            </svg>
          </button>
        </div>
      </div>

      {/* Node Categories */}
      <div className="flex-1 overflow-y-auto">
        <div className="p-4 space-y-6">
          {nodeCategories.map((category) => (
            <div key={category.title}>
              <h4 className="text-sm font-medium text-[#64748b] mb-3">
                {category.title}
              </h4>
              <div className="space-y-2">
                {category.nodes.map((node) => (
                  <div
                    key={node.id}
                    className={`flex items-center space-x-2.5 p-2.5 rounded-[6px] ${node.containerBg} border ${node.containerBorder} cursor-pointer ${node.hoverBg} transition-colors`}
                    draggable
                    onDragStart={(e) => {
                      e.dataTransfer.setData("application/reactflow", node.id);
                      e.dataTransfer.effectAllowed = "move";
                    }}
                  >
                    <div
                      className={`w-7 h-7 ${node.bgColor} rounded-[4px] flex items-center justify-center`}
                    >
                      {node.icon}
                    </div>
                    <span className="text-xs font-medium text-[#1e293b]">
                      {node.label}
                    </span>
                  </div>
                ))}
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default NodesSidebar;
