import React from "react";
import { useAppSelector, useAppDispatch } from "../../hooks/redux-hooks";
import { setCurrentFlow } from "../../redux/builder/builderSlice";

const FlowSidebar: React.FC = () => {
  const dispatch = useAppDispatch();
  const { flows, currentFlow } = useAppSelector((state) => state.builder);

  const handleFlowSelect = (flowId: string) => {
    const flow = flows.find((f) => f.id === flowId);
    if (flow) {
      dispatch(setCurrentFlow(flow));
    }
  };

  const addNewFlow = () => {
    // This would typically open a modal or form to create a new flow
    console.log("Add new flow");
  };

  // Default flows for display
  const defaultFlows = [
    { id: "welcome", name: "Welcome", status: "Default" },
    { id: "fallback", name: "Fallback", status: "Default" },
  ];

  return (
    <div className="w-[240px] bg-white border-r border-[#e2e8f0] flex flex-col">
      {/* Header */}
      <div className="px-4 py-3 border-b border-[#f1f5f9]">
        <div className="flex items-center justify-between">
          <h3 className="text-sm font-semibold text-[#1e293b]">Flows</h3>
          <button
            onClick={addNewFlow}
            className="w-5 h-5 bg-[#3b82f6] text-white rounded flex items-center justify-center hover:bg-[#2563eb] transition-colors"
          >
            <svg
              className="w-3 h-3"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M12 4v16m8-8H4"
              />
            </svg>
          </button>
        </div>
      </div>

      {/* Flow List */}
      <div className="flex-1 overflow-y-auto">
        <div className="px-3 py-2 space-y-1">
          {defaultFlows.map((flow) => (
            <div
              key={flow.id}
              className={`flex items-center justify-between px-3 py-2 rounded-md cursor-pointer transition-colors ${
                currentFlow?.id === flow.id
                  ? "bg-[#eff6ff] border border-[#dbeafe]"
                  : "hover:bg-[#f8fafc]"
              }`}
              onClick={() => handleFlowSelect(flow.id)}
            >
              <div className="flex items-center space-x-2.5">
                <div className="w-6 h-6 bg-[#f1f5f9] rounded-md flex items-center justify-center">
                  <svg
                    className="w-3.5 h-3.5 text-[#64748b]"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M13 10V3L4 14h7v7l9-11h-7z"
                    />
                  </svg>
                </div>
                <div>
                  <div className="text-sm font-medium text-[#1e293b]">
                    {flow.name}
                  </div>
                  <div className="text-xs text-[#64748b]">{flow.status}</div>
                </div>
              </div>
              <button className="p-1 text-[#94a3b8] hover:text-[#64748b] transition-colors">
                <svg
                  className="w-3.5 h-3.5"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M12 5v.01M12 12v.01M12 19v.01M12 6a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2z"
                  />
                </svg>
              </button>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default FlowSidebar;
