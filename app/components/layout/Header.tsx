import React from "react";

interface HeaderProps {
  title?: string;
  subtitle?: string;
  actions?: React.ReactNode;
}

const Header: React.FC<HeaderProps> = ({
  title = "NeuraTalk AI",
  subtitle = "NeuraTalk AI is a cutting-edge conversational AI solution designed to enhance customer engagement, automate support, and streamline business operations.",
  actions,
}) => {
  return (
    <div className="bg-white border-b border-[#e2e8f0] px-6 py-6">
      <div className="flex items-start justify-between">
        <div className="flex-1">
          <div className="flex items-start space-x-6">
            {/* Title and Description */}
            <div className="flex-1">
              <h1 className="text-2xl font-bold text-[#1e293b] mb-2">
                {title}
              </h1>
              <p className="text-sm text-[#64748b] leading-relaxed max-w-3xl">
                {subtitle}
              </p>
            </div>

            {/* Robot Avatar - Simplified Design */}
            <div className="flex-shrink-0">
              <div className="w-16 h-16 bg-gradient-to-br from-[#f97316] to-[#ea580c] rounded-full flex items-center justify-center shadow-sm">
                <div className="w-12 h-12 bg-white rounded-full flex items-center justify-center">
                  <div className="w-8 h-8 bg-gradient-to-br from-[#f97316] to-[#ea580c] rounded-full flex items-center justify-center">
                    <svg
                      className="w-5 h-5 text-white"
                      fill="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path d="M12 2C13.1 2 14 2.9 14 4C14 5.1 13.1 6 12 6C10.9 6 10 5.1 10 4C10 2.9 10.9 2 12 2ZM21 9V7L15 1H5C3.89 1 3 1.89 3 3V19C3 20.1 3.9 21 5 21H11V19H5V3H13V9H21ZM14 15.5L22.5 7L21 5.5L14 12.5L10.5 9L9 10.5L14 15.5Z" />
                    </svg>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Actions */}
        <div className="flex items-center space-x-4 ml-6">
          {/* Language Selector */}
          <div className="flex items-center space-x-2 px-2 py-1 bg-[#f8fafc] rounded-md">
            <span className="text-sm">🇺🇸</span>
            <span className="text-xs font-medium text-[#64748b]">EN</span>
          </div>

          {/* User Menu */}
          <div className="flex items-center space-x-2">
            <div className="w-8 h-8 bg-[#e2e8f0] rounded-full flex items-center justify-center">
              <svg
                className="w-4 h-4 text-[#64748b]"
                fill="currentColor"
                viewBox="0 0 24 24"
              >
                <path d="M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z" />
              </svg>
            </div>
          </div>

          {/* Custom Actions */}
          {actions}
        </div>
      </div>
    </div>
  );
};

export default Header;
