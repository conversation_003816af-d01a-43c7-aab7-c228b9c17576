import { useEffect } from 'react';
import { useAppDispatch } from '../hooks/redux-hooks';
import { hydrateAuth } from '../redux/auth/authSlice';

/**
 * Component that hydrates the auth state from localStorage on the client side
 * This prevents SSR/client hydration mismatches
 */
export default function AuthHydrator() {
  const dispatch = useAppDispatch();

  useEffect(() => {
    // Only run on client side
    if (typeof window !== 'undefined') {
      dispatch(hydrateAuth());
    }
  }, [dispatch]);

  // This component doesn't render anything
  return null;
}
