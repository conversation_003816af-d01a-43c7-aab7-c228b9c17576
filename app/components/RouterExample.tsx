import { Link } from "react-router";
import routes from "../router/routes";

/**
 * Example component demonstrating how to use the router configuration in a React component
 */
export default function RouterExample() {
  // Example agent ID and department
  const agentId = "123";
  const department = "sales";

  return (
    <div className="p-4">
      <h1 className="text-2xl font-bold mb-4">Router Example</h1>

      <h2 className="text-xl font-semibold mt-4 mb-2">Static Routes</h2>
      <ul className="list-disc pl-6 mb-4">
        <li>
          <Link to={routes.HOME}>Home</Link> - {routes.HOME}
        </li>
        <li>
          <Link to={routes.AGENTS}>Agents</Link> - {routes.AGENTS}
        </li>
        <li>
          <Link to={routes.NEW_AGENT}>New Agent</Link> - {routes.NEW_AGENT}
        </li>
      </ul>

      <h2 className="text-xl font-semibold mt-4 mb-2">
        Dynamic Routes with Parameters
      </h2>
      <ul className="list-disc pl-6 mb-4">
        <li>
          <Link to={routes.AGENT_WITH_ID(agentId)}>Agent {agentId}</Link> -{" "}
          {routes.AGENT_WITH_ID(agentId)}
        </li>
        <li>
          <Link to={routes.AGENT_WITH_ID_AND_DEPARTMENT(agentId, department)}>
            Agent {agentId} in {department}
          </Link>{" "}
          - {routes.AGENT_WITH_ID_AND_DEPARTMENT(agentId, department)}
        </li>
        <li>
          <Link to={routes.USER_PROFILE.invoke("456")}>
            User Profile (with invoke)
          </Link>{" "}
          - {routes.USER_PROFILE.invoke("456")}
        </li>
        <li>
          <Link to={routes.PRODUCT_DETAIL.invoke("electronics", "789")}>
            Product Detail (with invoke)
          </Link>{" "}
          - {routes.PRODUCT_DETAIL.invoke("electronics", "789")}
        </li>
      </ul>

      <h2 className="text-xl font-semibold mt-4 mb-2">
        Dynamic Routes as Raw Patterns
      </h2>
      <ul className="list-disc pl-6 mb-4">
        <li>
          {/* Now works without TypeScript errors! */}
          <Link to={routes.USER_PROFILE}>
            User Profile (raw pattern)
          </Link> - {routes.USER_PROFILE}
        </li>
        <li>
          {/* Now works without TypeScript errors! */}
          <Link to={routes.PRODUCT_DETAIL}>
            Product Detail (raw pattern)
          </Link> - {routes.PRODUCT_DETAIL}
        </li>
      </ul>

      <h2 className="text-xl font-semibold mt-4 mb-2">
        Direct Access to Route Patterns
      </h2>
      <ul className="list-disc pl-6 mb-4">
        {/* No need for String() conversion anymore */}
        <li>AGENT_WITH_ID direct access: {routes.AGENT_WITH_ID}</li>
        <li>
          AGENT_WITH_ID_AND_DEPARTMENT direct access:{" "}
          {routes.AGENT_WITH_ID_AND_DEPARTMENT}
        </li>
        <li>USER_PROFILE direct access: {routes.USER_PROFILE}</li>
        <li>PRODUCT_DETAIL direct access: {routes.PRODUCT_DETAIL}</li>
      </ul>

      <h2 className="text-xl font-semibold mt-4 mb-2">
        Route Patterns via .pattern Property
      </h2>
      <ul className="list-disc pl-6 mb-4">
        <li>AGENT_WITH_ID pattern: {routes.AGENT_WITH_ID.pattern}</li>
        <li>
          AGENT_WITH_ID_AND_DEPARTMENT pattern:{" "}
          {routes.AGENT_WITH_ID_AND_DEPARTMENT.pattern}
        </li>
        <li>USER_PROFILE pattern: {routes.USER_PROFILE.pattern}</li>
        <li>PRODUCT_DETAIL pattern: {routes.PRODUCT_DETAIL.pattern}</li>
      </ul>

      <h2 className="text-xl font-semibold mt-4 mb-2">
        String Operations with Routes
      </h2>
      <ul className="list-disc pl-6">
        <li>String concatenation: {"Path: " + routes.USER_PROFILE}</li>
        <li>String interpolation: {`Template: ${routes.PRODUCT_DETAIL}`}</li>
      </ul>
    </div>
  );
}
