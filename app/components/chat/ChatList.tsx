import React from 'react';
import { useAppSelector, useAppDispatch } from '../../hooks/redux-hooks';
import { setActiveSession, setStatusFilter, setSearchQuery } from '../../redux/chat/chatSlice';
import { ChatSession } from '../../types';

const ChatList: React.FC = () => {
  const dispatch = useAppDispatch();
  const { sessions, activeSession, statusFilter, searchQuery } = useAppSelector(state => state.chat);

  const statusCounts = {
    active: sessions.filter(s => s.status === 'active').length,
    queued: sessions.filter(s => s.status === 'queued').length,
    archived: sessions.filter(s => s.status === 'archived').length,
    missed: sessions.filter(s => s.status === 'missed').length
  };

  const filteredSessions = sessions.filter((session: ChatSession) => {
    const matchesSearch = session.userName.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         session.lastMessage.toLowerCase().includes(searchQuery.toLowerCase());
    const matchesStatus = statusFilter === 'all' || session.status === statusFilter;
    return matchesSearch && matchesStatus;
  });

  const getStatusColor = (status: ChatSession['status']) => {
    switch (status) {
      case 'active':
        return 'bg-green-500';
      case 'queued':
        return 'bg-blue-500';
      case 'archived':
        return 'bg-gray-400';
      case 'missed':
        return 'bg-red-500';
      default:
        return 'bg-gray-400';
    }
  };

  const getStatusLabel = (status: ChatSession['status']) => {
    switch (status) {
      case 'active':
        return 'Active';
      case 'queued':
        return 'Queued';
      case 'archived':
        return 'Archived';
      case 'missed':
        return 'Missed';
      default:
        return status;
    }
  };

  const handleSessionClick = (session: ChatSession) => {
    dispatch(setActiveSession(session));
  };

  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    dispatch(setSearchQuery(e.target.value));
  };

  const handleStatusFilterChange = (status: typeof statusFilter) => {
    dispatch(setStatusFilter(status));
  };

  return (
    <div className="w-80 bg-white border-r border-gray-200 flex flex-col">
      {/* Header */}
      <div className="p-4 border-b border-gray-200">
        <h2 className="text-xl font-semibold text-gray-900 mb-4">Chats</h2>
        
        {/* Status Filters */}
        <div className="flex space-x-1 mb-4">
          {[
            { key: 'active', label: 'Active', count: statusCounts.active },
            { key: 'queued', label: 'Queued', count: statusCounts.queued },
            { key: 'archived', label: 'Archived', count: statusCounts.archived },
            { key: 'missed', label: 'Missed', count: statusCounts.missed }
          ].map((filter) => (
            <button
              key={filter.key}
              onClick={() => handleStatusFilterChange(filter.key as typeof statusFilter)}
              className={`
                flex items-center space-x-1 px-3 py-1 rounded-full text-xs font-medium transition-colors
                ${statusFilter === filter.key
                  ? 'bg-blue-100 text-blue-700'
                  : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
                }
              `}
            >
              <span>{filter.label}</span>
              <span className="bg-white bg-opacity-70 px-1 rounded-full">
                {filter.count}
              </span>
            </button>
          ))}
        </div>

        {/* Search */}
        <div className="relative">
          <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
            <svg className="h-4 w-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
            </svg>
          </div>
          <input
            type="text"
            placeholder="Search conversations..."
            value={searchQuery}
            onChange={handleSearchChange}
            className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
          />
        </div>
      </div>

      {/* Chat Sessions */}
      <div className="flex-1 overflow-y-auto">
        {filteredSessions.length === 0 ? (
          <div className="text-center py-8">
            <div className="w-12 h-12 mx-auto mb-3 bg-gray-100 rounded-full flex items-center justify-center">
              <svg className="w-6 h-6 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
              </svg>
            </div>
            <p className="text-sm text-gray-500">No conversations found</p>
          </div>
        ) : (
          <div className="divide-y divide-gray-100">
            {filteredSessions.map((session) => (
              <div
                key={session.id}
                onClick={() => handleSessionClick(session)}
                className={`
                  p-4 cursor-pointer hover:bg-gray-50 transition-colors
                  ${activeSession?.id === session.id ? 'bg-blue-50 border-r-2 border-blue-500' : ''}
                `}
              >
                <div className="flex items-start space-x-3">
                  {/* Avatar */}
                  <div className="relative">
                    <div className="w-10 h-10 bg-gray-300 rounded-full flex items-center justify-center">
                      <span className="text-sm font-medium text-gray-600">
                        {session.userName.split(' ').map(n => n[0]).join('')}
                      </span>
                    </div>
                    <div className={`absolute -bottom-1 -right-1 w-3 h-3 rounded-full border-2 border-white ${getStatusColor(session.status)}`}></div>
                  </div>

                  {/* Content */}
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center justify-between">
                      <p className="text-sm font-medium text-gray-900 truncate">
                        {session.userName}
                      </p>
                      <p className="text-xs text-gray-500">
                        {session.timestamp}
                      </p>
                    </div>
                    <p className="text-sm text-gray-600 truncate mt-1">
                      {session.lastMessage}
                    </p>
                    <div className="flex items-center justify-between mt-2">
                      <span className={`
                        inline-flex items-center px-2 py-1 rounded-full text-xs font-medium
                        ${session.status === 'active' ? 'bg-green-100 text-green-800' :
                          session.status === 'queued' ? 'bg-blue-100 text-blue-800' :
                          session.status === 'archived' ? 'bg-gray-100 text-gray-800' :
                          'bg-red-100 text-red-800'
                        }
                      `}>
                        {getStatusLabel(session.status)}
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
};

export default ChatList;
