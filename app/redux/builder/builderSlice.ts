import { createSlice } from "@reduxjs/toolkit";
import type { PayloadAction } from "@reduxjs/toolkit";
import { Flow, FlowNode, Connection } from "../../types";

interface BuilderState {
  currentFlow: Flow | null;
  flows: Flow[];
  selectedNode: FlowNode | null;
  mode:
    | "design"
    | "train"
    | "channels"
    | "agent-transfer"
    | "integrations"
    | "settings";
  debuggerOpen: boolean;
  debugLogs: DebugLog[];
  previewOpen: boolean;
  previewMessages: PreviewMessage[];
}

interface DebugLog {
  id: string;
  type: "error" | "warning" | "info";
  message: string;
  timestamp: string;
  details?: string;
}

interface PreviewMessage {
  id: string;
  content: string;
  sender: "user" | "bot";
  timestamp: string;
}

const initialState: BuilderState = {
  currentFlow: {
    id: "flow-1",
    name: "new flow",
    nodes: [
      {
        id: "start-1",
        type: "start",
        position: { x: 250, y: 100 },
        data: { label: "Start" },
      },
    ],
    connections: [],
  },
  flows: [
    {
      id: "flow-1",
      name: "new flow",
      nodes: [
        {
          id: "start-1",
          type: "start",
          position: { x: 250, y: 100 },
          data: { label: "Start" },
        },
      ],
      connections: [],
    },
    {
      id: "flow-2",
      name: "Welcome",
      nodes: [],
      connections: [],
    },
    {
      id: "flow-3",
      name: "Fallback",
      nodes: [],
      connections: [],
    },
  ],
  selectedNode: null,
  mode: "design",
  debuggerOpen: false,
  debugLogs: [
    {
      id: "1",
      type: "error",
      message:
        "Failed to load resource: the server responded with a status of 404 (Not Found)",
      timestamp: "2024-01-22T10:30:00Z",
      details: "analytics.google.com_ga_collect?v=1&tid=UA-...",
    },
    {
      id: "2",
      type: "error",
      message:
        "Failed to load resource: the server responded with a status of 404 (Not Found)",
      timestamp: "2024-01-22T10:29:45Z",
      details: "analytics.google.com_ga_collect?v=1&tid=UA-...",
    },
    {
      id: "3",
      type: "warning",
      message:
        "Chrome is moving towards a new experience that allows users to choose to browse without third-party cookies.",
      timestamp: "2024-01-22T10:29:30Z",
    },
    {
      id: "4",
      type: "warning",
      message:
        "Chrome is moving towards a new experience that allows users to choose to browse without third-party cookies.",
      timestamp: "2024-01-22T10:29:15Z",
    },
  ],
  previewOpen: true,
  previewMessages: [
    {
      id: "1",
      content: "Hello there, Moi! How may I help you today?",
      sender: "bot",
      timestamp: "2024-01-22T10:30:00Z",
    },
  ],
};

const builderSlice = createSlice({
  name: "builder",
  initialState,
  reducers: {
    setCurrentFlow: (state, action: PayloadAction<Flow | null>) => {
      state.currentFlow = action.payload;
    },
    addNode: (state, action: PayloadAction<FlowNode>) => {
      if (state.currentFlow) {
        state.currentFlow.nodes.push(action.payload);
      }
    },
    updateNode: (state, action: PayloadAction<FlowNode>) => {
      if (state.currentFlow) {
        const index = state.currentFlow.nodes.findIndex(
          (node) => node.id === action.payload.id
        );
        if (index !== -1) {
          state.currentFlow.nodes[index] = action.payload;
        }
      }
    },
    deleteNode: (state, action: PayloadAction<string>) => {
      if (state.currentFlow) {
        state.currentFlow.nodes = state.currentFlow.nodes.filter(
          (node) => node.id !== action.payload
        );
        state.currentFlow.connections = state.currentFlow.connections.filter(
          (conn) =>
            conn.source !== action.payload && conn.target !== action.payload
        );
      }
    },
    addConnection: (state, action: PayloadAction<Connection>) => {
      if (state.currentFlow) {
        state.currentFlow.connections.push(action.payload);
      }
    },
    deleteConnection: (state, action: PayloadAction<string>) => {
      if (state.currentFlow) {
        state.currentFlow.connections = state.currentFlow.connections.filter(
          (conn) => conn.id !== action.payload
        );
      }
    },
    setSelectedNode: (state, action: PayloadAction<FlowNode | null>) => {
      state.selectedNode = action.payload;
    },
    setMode: (state, action: PayloadAction<BuilderState["mode"]>) => {
      state.mode = action.payload;
    },
    toggleDebugger: (state) => {
      state.debuggerOpen = !state.debuggerOpen;
    },
    addDebugLog: (state, action: PayloadAction<DebugLog>) => {
      state.debugLogs.unshift(action.payload);
    },
    clearDebugLogs: (state) => {
      state.debugLogs = [];
    },
    togglePreview: (state) => {
      state.previewOpen = !state.previewOpen;
    },
    addPreviewMessage: (state, action: PayloadAction<PreviewMessage>) => {
      state.previewMessages.push(action.payload);
    },
    clearPreviewMessages: (state) => {
      state.previewMessages = [];
    },
  },
});

export const {
  setCurrentFlow,
  addNode,
  updateNode,
  deleteNode,
  addConnection,
  deleteConnection,
  setSelectedNode,
  setMode,
  toggleDebugger,
  addDebugLog,
  clearDebugLogs,
  togglePreview,
  addPreviewMessage,
  clearPreviewMessages,
} = builderSlice.actions;

export default builderSlice.reducer;
