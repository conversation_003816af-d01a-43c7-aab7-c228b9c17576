import { configureStore } from "@reduxjs/toolkit";
import authReducer from "./auth/authSlice";
import chatbotsReducer from "./chatbots/chatbotsSlice";
import builderReducer from "./builder/builderSlice";
import chatReducer from "./chat/chatSlice";
import { apiSlice } from "./apiSlice";

export const store = configureStore({
  reducer: {
    auth: authReducer,
    chatbots: chatbotsReducer,
    builder: builderReducer,
    chat: chatReducer,
    [apiSlice.reducerPath]: apiSlice.reducer,
  },
});

export type RootState = ReturnType<typeof store.getState>;
export type AppDispatch = typeof store.dispatch;
