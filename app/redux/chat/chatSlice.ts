import { createSlice } from "@reduxjs/toolkit";
import type { PayloadAction } from "@reduxjs/toolkit";
import { ChatSession, ChatMessage } from "../../types";

interface ChatState {
  sessions: ChatSession[];
  activeSession: ChatSession | null;
  loading: boolean;
  error: string | null;
  statusFilter: "all" | "active" | "queued" | "archived" | "missed";
  searchQuery: string;
}

const initialState: ChatState = {
  sessions: [
    {
      id: "1",
      userId: "user-1",
      userName: "<PERSON>",
      status: "active",
      lastMessage: "Hello",
      timestamp: "12:30 PM",
      messages: [
        {
          id: "1",
          content: "Hello",
          sender: "user",
          timestamp: "12:30 PM",
        },
        {
          id: "2",
          content: "Hello! How can I assist you today?",
          sender: "bot",
          timestamp: "12:30 PM",
        },
      ],
    },
    {
      id: "2",
      userId: "user-2",
      userName: "<PERSON>",
      status: "active",
      lastMessage: "Hi",
      timestamp: "12:29 PM",
      messages: [
        {
          id: "1",
          content: "Hi",
          sender: "user",
          timestamp: "12:29 PM",
        },
      ],
    },
    {
      id: "3",
      userId: "user-3",
      userName: "Wayne Coyne",
      status: "queued",
      lastMessage: "Hello",
      timestamp: "12:29 PM",
      messages: [
        {
          id: "1",
          content: "Hello",
          sender: "user",
          timestamp: "12:29 PM",
        },
      ],
    },
    {
      id: "4",
      userId: "user-4",
      userName: "Adam Jones",
      status: "archived",
      lastMessage: "Yes, hello how can I he...",
      timestamp: "12:19 PM",
      messages: [
        {
          id: "1",
          content: "Yes, hello how can I help you?",
          sender: "user",
          timestamp: "12:19 PM",
        },
      ],
    },
    {
      id: "5",
      userId: "user-5",
      userName: "Josh Homme",
      status: "archived",
      lastMessage: "Hello",
      timestamp: "12:17 PM",
      messages: [
        {
          id: "1",
          content: "Hello",
          sender: "user",
          timestamp: "12:17 PM",
        },
      ],
    },
  ],
  activeSession: null,
  loading: false,
  error: null,
  statusFilter: "all",
  searchQuery: "",
};

const chatSlice = createSlice({
  name: "chat",
  initialState,
  reducers: {
    setSessions: (state, action: PayloadAction<ChatSession[]>) => {
      state.sessions = action.payload;
    },
    addSession: (state, action: PayloadAction<ChatSession>) => {
      state.sessions.unshift(action.payload);
    },
    updateSession: (state, action: PayloadAction<ChatSession>) => {
      const index = state.sessions.findIndex(
        (session) => session.id === action.payload.id
      );
      if (index !== -1) {
        state.sessions[index] = action.payload;
      }
    },
    setActiveSession: (state, action: PayloadAction<ChatSession | null>) => {
      state.activeSession = action.payload;
    },
    addMessage: (
      state,
      action: PayloadAction<{ sessionId: string; message: ChatMessage }>
    ) => {
      const { sessionId, message } = action.payload;
      const session = state.sessions.find((s) => s.id === sessionId);
      if (session) {
        session.messages.push(message);
        session.lastMessage = message.content;
        session.timestamp = message.timestamp;
      }
      if (state.activeSession && state.activeSession.id === sessionId) {
        state.activeSession.messages.push(message);
        state.activeSession.lastMessage = message.content;
        state.activeSession.timestamp = message.timestamp;
      }
    },
    updateSessionStatus: (
      state,
      action: PayloadAction<{
        sessionId: string;
        status: ChatSession["status"];
      }>
    ) => {
      const { sessionId, status } = action.payload;
      const session = state.sessions.find((s) => s.id === sessionId);
      if (session) {
        session.status = status;
      }
      if (state.activeSession && state.activeSession.id === sessionId) {
        state.activeSession.status = status;
      }
    },
    setLoading: (state, action: PayloadAction<boolean>) => {
      state.loading = action.payload;
    },
    setError: (state, action: PayloadAction<string | null>) => {
      state.error = action.payload;
    },
    setStatusFilter: (
      state,
      action: PayloadAction<ChatState["statusFilter"]>
    ) => {
      state.statusFilter = action.payload;
    },
    setSearchQuery: (state, action: PayloadAction<string>) => {
      state.searchQuery = action.payload;
    },
  },
});

export const {
  setSessions,
  addSession,
  updateSession,
  setActiveSession,
  addMessage,
  updateSessionStatus,
  setLoading,
  setError,
  setStatusFilter,
  setSearchQuery,
} = chatSlice.actions;

export default chatSlice.reducer;
