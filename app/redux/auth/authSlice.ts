import { createSlice } from "@reduxjs/toolkit";
import type { PayloadAction } from "@reduxjs/toolkit";
import type { RootState } from "../store";
import { safeLocalStorage } from "../../utils/localStorage";

export interface AuthState {
  isLoggedIn: boolean;
  accessToken: string | null;
  refreshToken: string | null;
  expires: number | null;
}

export interface AuthResData {
  access_token: string;
  expires_in: number;
  refresh_expires_in: number;
  refresh_token: string;
  token_type: string;
  "not-before-policy": number;
  session_state: string;
  scope: string;
}

// Create initial state that's safe for SSR
const createInitialState = (): AuthState => {
  // During SSR, return empty state
  if (typeof window === "undefined") {
    return {
      accessToken: null,
      refreshToken: null,
      expires: null,
      isLoggedIn: false,
    };
  }

  // On client, load from localStorage
  return {
    accessToken: safeLocalStorage.getItem("accessToken") ?? null,
    refreshToken: safeLocalStorage.getItem("refreshToken") ?? null,
    expires: +(safeLocalStorage.getItem("expires") || "0") || null,
    isLoggedIn: !!safeLocalStorage.getItem("accessToken"),
  };
};

const initialState: AuthState = createInitialState();

const authSlice = createSlice({
  name: "auth",
  initialState,
  reducers: {
    // Hydrate auth state from localStorage (client-side only)
    hydrateAuth: (state) => {
      if (typeof window !== "undefined") {
        const accessToken = safeLocalStorage.getItem("accessToken");
        const refreshToken = safeLocalStorage.getItem("refreshToken");
        const expires = safeLocalStorage.getItem("expires");

        state.accessToken = accessToken;
        state.refreshToken = refreshToken;
        state.expires = expires ? parseInt(expires, 10) : null;
        state.isLoggedIn = !!accessToken;
      }
    },
    setCredentials: (state, action: PayloadAction<AuthResData>) => {
      const { access_token, refresh_token, expires_in } = action.payload;
      state.accessToken = access_token;
      state.refreshToken = refresh_token;
      state.expires = expires_in;
      state.isLoggedIn = true;

      // Persist to localStorage safely
      safeLocalStorage.setItem("accessToken", access_token);
      safeLocalStorage.setItem("refreshToken", refresh_token);
      safeLocalStorage.setItem("expires", expires_in.toString());
    },
    unsetCredentials: (state) => {
      state.accessToken = null;
      state.refreshToken = null;
      state.expires = null;
      state.isLoggedIn = false;

      // Remove from localStorage safely
      safeLocalStorage.removeItem("accessToken");
      safeLocalStorage.removeItem("refreshToken");
      safeLocalStorage.removeItem("expires");
    },
  },
});

export const { hydrateAuth, setCredentials, unsetCredentials } =
  authSlice.actions;

export default authSlice.reducer;

// Selectors
export const selectCurrentLoginStatus = (state: RootState) =>
  state.auth.isLoggedIn;
export const selectCurrentAccessToken = (state: RootState) =>
  state.auth.accessToken;
export const selectCurrentRefreshToken = (state: RootState) =>
  state.auth.refreshToken;
export const selectCurrentAuthState = (state: RootState) => state.auth;
