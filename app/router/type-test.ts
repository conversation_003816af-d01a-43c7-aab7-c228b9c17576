/**
 * This file demonstrates the enhanced type safety of our router implementation.
 * It's not meant to be executed, but rather to show TypeScript's type checking.
 */
import routes from "./routes";
import { <PERSON> } from "react-router";

// Type tests for static routes
// These should all be inferred as string literal types
const home: "/" = routes.HOME;
const agents: "/agents" = routes.AGENTS;
const newAgent: "/agents/new" = routes.NEW_AGENT;

// Type tests for dynamic routes
// These should all be inferred as string literal types when accessed directly
const agentWithIdPattern: "/agents/[id]" = routes.AGENT_WITH_ID;
const agentWithIdAndDeptPattern: "/agents/[id]/[department]" =
  routes.AGENT_WITH_ID_AND_DEPARTMENT;
const userProfilePattern: "/users/[userId]/profile" = routes.USER_PROFILE;
const productDetailPattern: "/products/[category]/[productId]" =
  routes.PRODUCT_DETAIL;

// Type tests for dynamic routes via .pattern property
// These should all be inferred as string literal types
const agentWithIdPatternViaProp: "/agents/[id]" = routes.AGENT_WITH_ID.pattern;
const userProfilePatternViaProp: "/users/[userId]/profile" =
  routes.USER_PROFILE.pattern;

// Type tests for string operations
// These should all be inferred as string types
const stringConcat: string = "Path: " + routes.USER_PROFILE;
const stringInterpolation: string = `Template: ${routes.PRODUCT_DETAIL}`;

// Type tests for function calls
// These should all be inferred as string types (not literal types since they're dynamic)
const agentWithIdUrl: string = routes.AGENT_WITH_ID("123");
const agentWithIdAndDeptUrl: string = routes.AGENT_WITH_ID_AND_DEPARTMENT(
  "123",
  "sales"
);

// Type tests for invoke method
// These should all be inferred as string types (not literal types since they're dynamic)
const userProfileUrl: string = routes.USER_PROFILE.invoke("456");
const productDetailUrl: string = routes.PRODUCT_DETAIL.invoke(
  "electronics",
  "789"
);

// Type tests for withParams method
// These should all be inferred as string types (not literal types since they're dynamic)
const userProfileUrlNamed: string = routes.USER_PROFILE.withParams({
  userId: "456",
});
const productDetailUrlNamed: string = routes.PRODUCT_DETAIL.withParams({
  category: "electronics",
  productId: "789",
});

// Type tests for paramNames property
// These should be inferred as readonly string arrays with the correct parameter names
const userProfileParams: readonly ["userId"] = routes.USER_PROFILE.paramNames;
const productDetailParams: readonly ["category", "productId"] =
  routes.PRODUCT_DETAIL.paramNames;

// Type tests for React Router's Link component
// These should all work without type errors
function TestLinks() {
  return (
    <div>
      {/* Static routes */}
      <Link to={routes.HOME}>Home</Link>
      <Link to={routes.AGENTS}>Agents</Link>

      {/* Dynamic routes with parameters */}
      <Link to={routes.AGENT_WITH_ID("123")}>Agent 123</Link>
      <Link to={routes.USER_PROFILE.invoke("456")}>User Profile</Link>

      {/* Dynamic routes as raw patterns - now works without type errors! */}
      <Link to={routes.AGENT_WITH_ID}>Agent Pattern</Link>
      <Link to={routes.USER_PROFILE}>User Profile Pattern</Link>
    </div>
  );
}

// Type tests for parameter count validation
// TypeScript should catch these errors at compile time
// @ts-expect-error - AGENT_WITH_ID requires exactly 1 parameter
routes.AGENT_WITH_ID();

// @ts-expect-error - AGENT_WITH_ID requires exactly 1 parameter
routes.AGENT_WITH_ID("123", "extra");

// @ts-expect-error - AGENT_WITH_ID_AND_DEPARTMENT requires exactly 2 parameters
routes.AGENT_WITH_ID_AND_DEPARTMENT("123");

// @ts-expect-error - USER_PROFILE requires exactly 1 parameter
routes.USER_PROFILE.invoke();

// @ts-expect-error - PRODUCT_DETAIL requires exactly 2 parameters
routes.PRODUCT_DETAIL.invoke("electronics", "789", "extra");

// Type tests for withParams method validation
// @ts-expect-error - Missing required parameter "userId"
routes.USER_PROFILE.withParams({});

// @ts-expect-error - Unknown parameter "wrongParam"
routes.USER_PROFILE.withParams({ userId: "456", wrongParam: "value" });

// @ts-expect-error - Missing required parameter "productId"
routes.PRODUCT_DETAIL.withParams({ category: "electronics" });
