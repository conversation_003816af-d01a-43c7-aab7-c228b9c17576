/**
 * Router configuration with support for both static and dynamic routes.
 *
 * Features:
 * - Access static routes directly via dot notation (e.g., routes.HOME)
 * - Access dynamic route patterns directly (e.g., routes.AGENT_WITH_ID returns "/agent/[id]")
 * - Generate URLs for dynamic routes by passing parameters:
 *   - Function call: routes.AGENT_WITH_ID(id, department)
 *   - Method invocation: routes.AGENT_WITH_ID.invoke(id, department)
 */

// Type for route parameters
type RouteParams = string | number;

// Type for named route parameters (removed - using ParamsObject instead)

/**
 * Generic interface for a dynamic route that can be both called as a function
 * and accessed directly as a string (returning the pattern)
 *
 * @template P - The string literal type for the pattern
 * @template ParamNames - Tuple type of parameter names
 */
interface DynamicRouteMethods<
  P extends string,
  ParamNames extends readonly string[] = string[]
> {
  // When accessed directly as a string, returns the raw path pattern
  toString(): P;

  // String conversion for template literals and concatenation
  [Symbol.toPrimitive](hint: string): P;

  // Function to generate a URL with parameters
  (...params: Tuple<RouteParams, ParamNames["length"]>): string;

  // Alternative method to generate a URL with parameters - using named parameters
  withParams<T extends ParamsObject<ParamNames>>(
    params: T & Record<Exclude<keyof T, ParamNames[number]>, never>
  ): string;

  // Alternative method to generate a URL with parameters - using positional parameters
  invoke(...params: Tuple<RouteParams, ParamNames["length"]>): string;

  // The raw path pattern (e.g., "/agent/[id]/[department]")
  readonly pattern: P;

  // The parameter names extracted from the pattern
  readonly paramNames: readonly [...ParamNames];
}

/**
 * Type that makes a dynamic route directly assignable to a string
 * This is the key to making dynamic routes work with React Router's Link component
 * and other contexts that expect string types
 */
type DynamicRoute<
  P extends string,
  ParamNames extends readonly string[] = string[]
> = P & DynamicRouteMethods<P, ParamNames>;

/**
 * Helper type to extract parameter names from a route pattern
 *
 * @template P - The string literal type for the pattern
 */
type ExtractParamNames<P extends string> =
  P extends `${string}[${infer Param}]${infer Rest}`
    ? [Param, ...ExtractParamNames<Rest>]
    : [];

/**
 * Helper type to create a tuple of the same type with a specific length
 *
 * @template T - The type to repeat
 * @template N - The length of the tuple
 * @template Acc - Accumulator for the recursive type
 */
type Tuple<T, N extends number, Acc extends T[] = []> = Acc["length"] extends N
  ? Acc
  : Tuple<T, N, [...Acc, T]>;

// ParamTuple type removed - using Tuple<RouteParams, N> directly

/**
 * Helper type to create a strongly-typed parameter object from parameter names
 *
 * @template Names - Tuple of parameter names
 */
type ParamsObject<Names extends readonly string[]> = {
  [K in Names[number]]: RouteParams;
};

/**
 * Type for the router constant object with strongly typed routes
 */
export type RouterConstant<
  T extends Record<string, string | DynamicRoute<string>>
> = {
  readonly [K in keyof T]: T[K];
};

/**
 * Creates a dynamic route with parameter substitution capabilities
 * that can also be used directly as a string
 *
 * @param pattern The route pattern with parameters in [param] format
 * @returns A function that can be called with parameters to generate a URL
 *          or accessed directly as a string to get the pattern
 */
function createDynamicRoute<P extends string>(
  pattern: P
): DynamicRoute<P, ExtractParamNames<P>> {
  // Extract parameter names from the pattern
  const paramNames =
    pattern
      .match(/\[([^\]]+)\]/g)
      ?.map((param) => param.substring(1, param.length - 1)) || [];

  /**
   * Helper function to generate a URL by replacing parameters in the pattern
   */
  const generateUrl = (params: RouteParams[]): string => {
    if (params.length !== paramNames.length) {
      throw new Error(
        `Route "${pattern}" requires ${paramNames.length} parameters, but ${params.length} were provided.`
      );
    }

    // Replace each parameter in the pattern
    let result = pattern as string;
    for (let i = 0; i < paramNames.length; i++) {
      result = result.replace(`[${paramNames[i]}]`, String(params[i]));
    }

    return result;
  };

  // Create the route function
  const routeFunction = function (...params: RouteParams[]): string {
    // If no parameters are provided, return the pattern
    if (params.length === 0 && paramNames.length > 0) {
      throw new Error(
        `Route "${pattern}" requires ${paramNames.length} parameters, but 0 were provided.`
      );
    }

    return generateUrl(params);
  };

  // Add toString method to return the pattern when used as a string
  Object.defineProperty(routeFunction, "toString", {
    value: function (): P {
      return pattern;
    },
    writable: false,
    enumerable: false,
  });

  // Add the pattern property
  Object.defineProperty(routeFunction, "pattern", {
    value: pattern,
    writable: false,
    enumerable: true,
  });

  // Add the paramNames property
  Object.defineProperty(routeFunction, "paramNames", {
    value: Object.freeze([...paramNames]),
    writable: false,
    enumerable: true,
  });

  // Add the invoke method (positional parameters)
  Object.defineProperty(routeFunction, "invoke", {
    value: function (...params: RouteParams[]): string {
      // If no parameters are provided and the route has parameters, return the pattern
      if (params.length === 0) {
        if (paramNames.length === 0) {
          return pattern;
        }
        throw new Error(
          `Route "${pattern}" requires ${paramNames.length} parameters, but 0 were provided.`
        );
      }

      return generateUrl(params);
    },
    writable: false,
    enumerable: true,
  });

  // Add the withParams method (named parameters)
  Object.defineProperty(routeFunction, "withParams", {
    value: function <T extends Record<string, RouteParams>>(
      params: T & Record<Exclude<keyof T, string>, never>
    ): string {
      // Check for extra parameters
      const extraParams = Object.keys(params).filter(
        (key) => !paramNames.includes(key)
      );
      if (extraParams.length > 0) {
        throw new Error(
          `Unknown parameter(s) for route "${pattern}": ${extraParams.join(
            ", "
          )}`
        );
      }

      // Convert named parameters to positional parameters
      const positionalParams = paramNames.map((name) => {
        if (!(name in params)) {
          throw new Error(`Missing parameter "${name}" for route "${pattern}"`);
        }
        return params[name];
      });

      return generateUrl(positionalParams);
    },
    writable: false,
    enumerable: true,
  });

  // Make the function work with string concatenation and interpolation
  Object.defineProperty(routeFunction, Symbol.toPrimitive, {
    value: function (_hint: string): P {
      return pattern;
    },
    writable: false,
    enumerable: false,
  });

  return routeFunction as unknown as DynamicRoute<P, ExtractParamNames<P>>;
}

/**
 * Router constant object with both static and dynamic routes
 */
const routes = {
  // Static routes
  HOME: "/",
  AGENTS: "/agents",
  NEW_AGENT: "/agents/new",

  // Dynamic routes
  AGENT_WITH_ID: createDynamicRoute("/agents/[id]"),
  AGENT_WITH_ID_AND_DEPARTMENT: createDynamicRoute("/agents/[id]/[department]"),
  USER_PROFILE: createDynamicRoute("/users/[userId]/profile"),
  PRODUCT_DETAIL: createDynamicRoute("/products/[category]/[productId]"),
} as const;

// Export the routes with proper typing
export default routes as RouterConstant<typeof routes>;
