/**
 * Example usage of the router configuration with enhanced type safety
 */
import routes from "./routes";

// Examples of using static routes
// TypeScript now infers these as string literal types
console.log("Static route HOME:", routes.HOME); // Output: /
console.log("Static route AGENTS:", routes.AGENTS); // Output: /agents
console.log("Static route NEW_AGENT:", routes.NEW_AGENT); // Output: /agents/new

// Examples of accessing dynamic route patterns directly
// TypeScript now infers these as string literal types
console.log(
  "Dynamic route AGENT_WITH_ID (direct access):",
  routes.AGENT_WITH_ID
);
// Output: /agents/[id]

console.log(
  "Dynamic route AGENT_WITH_ID_AND_DEPARTMENT (direct access):",
  routes.AGENT_WITH_ID_AND_DEPARTMENT
);
// Output: /agents/[id]/[department]

// String concatenation with dynamic routes
// TypeScript correctly handles string operations
console.log(
  "String concatenation with dynamic route:",
  "Path: " + routes.USER_PROFILE
);
// Output: Path: /users/[userId]/profile

// String interpolation with dynamic routes
console.log(
  `String interpolation with dynamic route: ${routes.PRODUCT_DETAIL}`
);
// Output: String interpolation with dynamic route: /products/[category]/[productId]

// The pattern property still works and has the correct type
console.log(
  "Dynamic route pattern via .pattern property:",
  routes.AGENT_WITH_ID.pattern
);
// Output: /agents/[id]

// Examples of generating URLs for dynamic routes using function call syntax
// TypeScript enforces the correct number of parameters
console.log(
  "Dynamic route AGENT_WITH_ID with parameter:",
  routes.AGENT_WITH_ID("123")
);
// Output: /agents/123

console.log(
  "Dynamic route AGENT_WITH_ID_AND_DEPARTMENT with parameters:",
  routes.AGENT_WITH_ID_AND_DEPARTMENT("123", "sales")
);
// Output: /agents/123/sales

// Examples of generating URLs for dynamic routes using method invocation
// TypeScript enforces the correct number of parameters
console.log(
  "Dynamic route USER_PROFILE with invoke method:",
  routes.USER_PROFILE.invoke("456")
);
// Output: /users/456/profile

console.log(
  "Dynamic route PRODUCT_DETAIL with invoke method:",
  routes.PRODUCT_DETAIL.invoke("electronics", "789")
);
// Output: /products/electronics/789

// NEW FEATURE: Using named parameters with withParams method
console.log(
  "Dynamic route USER_PROFILE with withParams method:",
  routes.USER_PROFILE.withParams({ userId: "456" })
);
// Output: /users/456/profile

console.log(
  "Dynamic route PRODUCT_DETAIL with withParams method:",
  routes.PRODUCT_DETAIL.withParams({
    category: "electronics",
    productId: "789",
  })
);
// Output: /products/electronics/789

// NEW FEATURE: Accessing parameter names
console.log(
  "Parameter names for USER_PROFILE:",
  routes.USER_PROFILE.paramNames
);
// Output: ["userId"]

console.log(
  "Parameter names for PRODUCT_DETAIL:",
  routes.PRODUCT_DETAIL.paramNames
);
// Output: ["category", "productId"]

// TypeScript will catch these errors at compile time:
// routes.AGENT_WITH_ID(); // Error: Expected 1 arguments, but got 0
// routes.AGENT_WITH_ID("123", "extra"); // Error: Expected 1 arguments, but got 2
// routes.USER_PROFILE.invoke(); // Error: Expected 1 arguments, but got 0

// Runtime error handling when incorrect number of parameters is provided
try {
  // This will throw an error because AGENT_WITH_ID_AND_DEPARTMENT requires 2 parameters
  // @ts-expect-error - TypeScript catches this, but we're demonstrating runtime behavior
  routes.AGENT_WITH_ID_AND_DEPARTMENT("123");
} catch (error) {
  console.error("Error:", (error as Error).message);
  // Output: Error: Route "/agents/[id]/[department]" requires 2 parameters, but 1 were provided.
}
