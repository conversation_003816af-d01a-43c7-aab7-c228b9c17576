import { type RouteConfig, route, index } from "@react-router/dev/routes";
import routes from "./router/routes";

export default [
  index("routes/Home/index.tsx"), // Default route
  route(routes.AGENTS, "routes/Agents/index.tsx", [
    // route("new", "routes/Agents/new.tsx"), // Nested route /agents/new
  ]), // Nested route /agents
  route(routes.NEW_AGENT, "routes/Agents/new.tsx"), // Dynamic route /agents/:id
  route("builder/:id", "routes/Builder/index.tsx"), // Chatbot builder
  route("chat", "routes/Chat/index.tsx"), // Chat interface
  route("settings/:id", "routes/Settings/index.tsx"), // Settings page
] satisfies RouteConfig;
