// Chatbot types
export interface Chatbot {
  id: string;
  name: string;
  description: string;
  status: 'live' | 'draft';
  lastUpdated: string;
  createdAt: string;
  flows: Flow[];
}

// Flow builder types
export interface Flow {
  id: string;
  name: string;
  nodes: FlowNode[];
  connections: Connection[];
}

export interface FlowNode {
  id: string;
  type: 'start' | 'message' | 'interactive' | 'feedback' | 'notification';
  position: { x: number; y: number };
  data: NodeData;
}

export interface NodeData {
  label?: string;
  message?: string;
  options?: string[];
  [key: string]: any;
}

export interface Connection {
  id: string;
  source: string;
  target: string;
  sourceHandle?: string;
  targetHandle?: string;
}

// Chat types
export interface ChatMessage {
  id: string;
  content: string;
  sender: 'user' | 'bot';
  timestamp: string;
  type?: 'text' | 'options' | 'feedback';
}

export interface ChatSession {
  id: string;
  userId: string;
  userName: string;
  status: 'active' | 'queued' | 'archived' | 'missed';
  lastMessage: string;
  timestamp: string;
  messages: ChatMessage[];
}

// Settings types
export interface ChatbotSettings {
  id: string;
  language: LanguageSettings;
  nlu: NLUSettings;
  llm: LLMSettings;
  contentResources: ContentResourcesSettings;
  personalization: PersonalizationSettings;
}

export interface LanguageSettings {
  primary: string;
  supported: string[];
  autoDetect: boolean;
}

export interface NLUSettings {
  provider: string;
  confidence: number;
  fallbackEnabled: boolean;
}

export interface LLMSettings {
  provider: string;
  model: string;
  temperature: number;
  maxTokens: number;
}

export interface ContentResourcesSettings {
  knowledgeBase: string[];
  documents: string[];
  urls: string[];
}

export interface PersonalizationSettings {
  enabled: boolean;
  userProfiling: boolean;
  contextRetention: number;
}

// UI State types
export interface UIState {
  sidebarCollapsed: boolean;
  activeTab: string;
  selectedChatbot: string | null;
  builderMode: 'design' | 'train' | 'channels' | 'agent-transfer' | 'integrations' | 'settings';
}
