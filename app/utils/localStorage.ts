/**
 * Safe localStorage utility that works in both SSR and client environments
 */

export const safeLocalStorage = {
  getItem: (key: string): string | null => {
    if (typeof window !== 'undefined' && window.localStorage) {
      try {
        return localStorage.getItem(key);
      } catch (error) {
        console.warn(`Failed to get item from localStorage: ${error}`);
        return null;
      }
    }
    return null;
  },

  setItem: (key: string, value: string): void => {
    if (typeof window !== 'undefined' && window.localStorage) {
      try {
        localStorage.setItem(key, value);
      } catch (error) {
        console.warn(`Failed to set item in localStorage: ${error}`);
      }
    }
  },

  removeItem: (key: string): void => {
    if (typeof window !== 'undefined' && window.localStorage) {
      try {
        localStorage.removeItem(key);
      } catch (error) {
        console.warn(`Failed to remove item from localStorage: ${error}`);
      }
    }
  },

  clear: (): void => {
    if (typeof window !== 'undefined' && window.localStorage) {
      try {
        localStorage.clear();
      } catch (error) {
        console.warn(`Failed to clear localStorage: ${error}`);
      }
    }
  }
};

export default safeLocalStorage;
