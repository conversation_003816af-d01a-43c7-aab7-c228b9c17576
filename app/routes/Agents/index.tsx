import type { Route } from ".react-router/types/app/+types/root";
import { Outlet } from "react-router";

export function meta({}: Route.MetaArgs) {
  return [
    { title: "Agent Page" },
    { name: "description", content: "All agents!" },
  ];
}

export default function Agents() {
  return (
    <main className="flex items-center justify-center pt-16 pb-4">
      <div className="flex-1 flex flex-col items-center gap-16 min-h-0">
        <header className="flex flex-col items-center gap-9">Agents</header>

        <Outlet />
      </div>
    </main>
  );
}
