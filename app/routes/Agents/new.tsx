import type { Route } from ".react-router/types/app/+types/root";

export function meta({}: Route.MetaArgs) {
  return [
    { title: "New Agent Page" },
    { name: "description", content: "All agents!" },
  ];
}

export default function NewAgents() {
  return (
    <main className="flex items-center justify-center pt-16 pb-4 bg-red">
      <div className="flex-1 flex flex-col items-center gap-16 min-h-0">
        <header className="flex flex-col items-center gap-9">New Agents</header>
      </div>
    </main>
  );
}
