import React from "react";
import type { Route } from "./+types";
import Layout from "../../components/layout/Layout";
import SearchAndFilters from "../../components/dashboard/SearchAndFilters";
import ChatbotGrid from "../../components/dashboard/ChatbotGrid";

export function meta({}: Route.MetaArgs) {
  return [
    { title: "NeuraTalk AI - Dashboard" },
    { name: "description", content: "Manage your chatbots with NeuraTalk AI" },
  ];
}

export default function Home() {
  return (
    <Layout>
      <div className="min-h-screen bg-[#fafbfc] p-6">
        <div className="max-w-[1400px] mx-auto">
          <SearchAndFilters />
          <div className="mt-6">
            <ChatbotGrid />
          </div>
        </div>
      </div>
    </Layout>
  );
}
