/**
 * Custom client entry point to ensure compatibility with both React Router and Module Federation
 */

import { HydratedRouter } from "react-router/dom";
import { startTransition, StrictMode } from "react";
import { hydrateRoot } from "react-dom/client";
import { Provider } from "react-redux";
import { store } from "./redux/store";

startTransition(() => {
  hydrateRoot(
    document,
    <StrictMode>
      <Provider store={store}>
        <HydratedRouter />
      </Provider>
    </StrictMode>
  );
});
