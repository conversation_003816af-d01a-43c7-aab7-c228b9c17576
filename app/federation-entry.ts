/**
 * This is the main entry point for the micro-frontend when used in a federated setup.
 * It exports all the components, routes, and utilities that should be available to the host application.
 */

// Export the main App component
export { default as App } from "./root";

// Export routes configuration
export { default as routes } from "./router/routes";

// Export Redux store
export { store } from "./redux/store";
export type { RootState, AppDispatch } from "./redux/store";

// Export Redux hooks
export { useAppDispatch, useAppSelector } from "./hooks/redux-hooks";

// Export components
export { default as RouterExample } from "./components/RouterExample";
export { default as MicroFrontendDemo } from "./components/MicroFrontendDemo";
export { default as SimpleTestComponent } from "./components/SimpleTestComponent";

// Export route components
export { default as Home } from "./routes/Home/index";
export { default as Agents } from "./routes/Agents/index";
export { default as NewAgents } from "./routes/Agents/new";

// Export API slice for data fetching
export { apiSlice } from "./redux/apiSlice";

// Export auth slice for authentication
export {
  default as authReducer,
  setCredentials,
  unsetCredentials,
  selectCurrentLoginStatus,
  selectCurrentAccessToken,
  selectCurrentRefreshToken,
  selectCurrentAuthState,
} from "./redux/auth/authSlice";
